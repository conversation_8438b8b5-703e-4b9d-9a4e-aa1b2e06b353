{"call_id": "ebfd4f99-1d07-4690-b717-45bc2f27a78a", "timestamp": "2025-07-29T09:58:30.205993", "provider": "gemini", "call_type": "initial", "conversation_id": null, "latency_seconds": 0.00014519691467285156, "request": {"model": "gemini-2.5-flash-preview-04-17", "messages": [{"role": "user", "content": "Welcome to the Chat Terminal"}, {"role": "assistant", "content": "Error: Unknown field for <PERSON><PERSON><PERSON>: title"}, {"role": "user", "content": "============================================================"}, {"role": "assistant", "content": "Error: Unknown field for <PERSON><PERSON><PERSON>: title"}, {"role": "user", "content": "Type messages to chat with the assistant."}, {"role": "assistant", "content": "Error: Unknown field for <PERSON><PERSON><PERSON>: title"}, {"role": "user", "content": "Commands start with '/' (e.g., '/help')."}, {"role": "assistant", "content": "Error: Unknown field for <PERSON><PERSON><PERSON>: title"}, {"role": "user", "content": "============================================================"}, {"role": "assistant", "content": "Error: Unknown field for <PERSON><PERSON><PERSON>: title"}, {"role": "user", "content": "Storage directory: /home/<USER>/django-projects/agbase_admin/conversations"}, {"role": "assistant", "content": "Error: Unknown field for <PERSON><PERSON><PERSON>: title"}, {"role": "user", "content": "User ID: sam"}], "tools": [{"name": "echostring", "description": "Echo a string\n\n    Args:\n        phrase: the phrase to echo\n    ", "input_schema": {"properties": {"phrase": {"description": "Phrase to echo", "title": "Phrase", "type": "string"}}, "required": ["phrase"], "title": "echostringArguments", "type": "object"}}, {"name": "babyfetch_multiple_simple", "description": "\n    Fetch content from multiple URLs efficiently.\n    \n    Args:\n        urls: List of URLs to fetch\n        max_text_length: Maximum text length per URL\n    \n    Returns:\n        Dict with results list and summary\n    ", "input_schema": {"properties": {"urls": {"items": {"type": "string"}, "title": "Urls", "type": "array"}, "max_text_length": {"default": 1000, "title": "Max Text Length", "type": "integer"}}, "required": ["urls"], "title": "babyfetch_multiple_simpleArguments", "type": "object"}}, {"name": "babyfetch_simple", "description": "\n    Fetch and parse content from a URL using BabySpider with size limits.\n    \n    Args:\n        url: The URL to fetch content from\n        max_text_length: Maximum length of text content to return (default: 1500 chars)\n    \n    Returns:\n        Dict containing:\n        - success: bool - Whether the fetch was successful\n        - url: str - The original URL\n        - content: dict - Parsed content (text, title, links summary)\n        - crawl_info: dict - Crawl metadata (status_code, domain, etc.)\n        - error: str - Error message if failed\n    ", "input_schema": {"properties": {"url": {"title": "Url", "type": "string"}, "max_text_length": {"default": 1500, "title": "Max Text Length", "type": "integer"}}, "required": ["url"], "title": "babyfetch_simpleArguments", "type": "object"}}, {"name": "babyfetch_text_only", "description": "\n    Fetch only the text content from a URL (most efficient).\n    \n    Args:\n        url: The URL to fetch content from\n        max_length: Maximum length of text to return\n    \n    Returns:\n        Dict with success, url, text, title, and basic info\n    ", "input_schema": {"properties": {"url": {"title": "Url", "type": "string"}, "max_length": {"default": 1000, "title": "Max Length", "type": "integer"}}, "required": ["url"], "title": "babyfetch_text_onlyArguments", "type": "object"}}, {"name": "babyfetch_url", "description": "\n    Fetch and parse content from a URL using CachedBabyFetch.\n    \n    Args:\n        url: The URL to fetch content from\n        max_age: Maximum age in seconds for cached content (None = no age limit)\n        refresh_cache: If True, bypass cache and fetch fresh content\n    \n    Returns:\n        Dict containing:\n        - success: bool - Whether the fetch was successful\n        - url: str - The original URL\n        - cache_hit: bool - Whether result came from cache\n        - content: dict - Parsed content (html, text, markdown, etc.)\n        - crawl_info: dict - Crawl metadata (status_code, headers, etc.)\n        - error: str - Error message if failed\n    ", "input_schema": {"properties": {"url": {"title": "Url", "type": "string"}, "max_text_length": {"default": 2000, "title": "Max Text Length", "type": "integer"}, "include_html": {"default": false, "title": "Include Html", "type": "boolean"}, "max_age": {"default": null, "title": "Max Age", "type": "integer"}, "refresh_cache": {"default": false, "title": "<PERSON><PERSON><PERSON>", "type": "boolean"}}, "required": ["url"], "title": "babyfetch_urlArguments", "type": "object"}}, {"name": "create_context", "description": "\n    Create a new AsContext record\n    \n    Args:\n        name: Context name (required)\n        context_type: Type ('draft', 'tag', 'client', 'compete', 'junk')\n        keywords: Keywords text\n        comments: Comments text\n        created_by_id: User ID who created this context\n        tagtree_id: TagTree ID if applicable\n        \n    Returns:\n        Created context dictionary or None if failed\n    ", "input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "context_type": {"default": "draft", "title": "Context Type", "type": "string"}, "keywords": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Keywords"}, "comments": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Comments"}, "created_by_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Created By Id"}, "tagtree_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Tagtree Id"}}, "required": ["name"], "title": "create_contextArguments", "type": "object"}}, {"name": "create_rating", "description": "\n    Create a new AsRating record\n    \n    Args:\n        es_id: Elasticsearch document ID (required)\n        context_id: AsContext ID this rating belongs to (required)\n        rating: Rating value (typically -1, 0, or 1)\n        created_by_id: User ID who created this rating\n        es_idx: Elasticsearch index name\n        goid: Additional identifier\n        \n    Returns:\n        Created rating dictionary or None if failed\n    ", "input_schema": {"properties": {"es_id": {"title": "Es Id", "type": "string"}, "context_id": {"title": "Context Id", "type": "integer"}, "rating": {"default": 0, "title": "Rating", "type": "integer"}, "created_by_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Created By Id"}, "es_idx": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Es Idx"}, "goid": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}}, "required": ["es_id", "context_id"], "title": "create_ratingArguments", "type": "object"}}, {"name": "debug_database_connection", "description": "\n    Test database connection and query capabilities\n    \n    Args:\n        database_name: Name of database connection to test (default: 'legacy')\n        \n    Returns:\n        Dictionary with connection test results\n    ", "input_schema": {"properties": {"database_name": {"default": "legacy", "title": "Database Name", "type": "string"}}, "title": "debug_database_connectionArguments", "type": "object"}}, {"name": "debug_django_setup", "description": "\n    Verify Django configuration and app loading\n    \n    Returns:\n        Dictionary with Django setup diagnostics\n    ", "input_schema": {"properties": {}, "title": "debug_django_setupArguments", "type": "object"}}, {"name": "debug_mcp_context", "description": "\n    Debug MCP execution context and environment\n    \n    Returns:\n        Dictionary with MCP context diagnostics\n    ", "input_schema": {"properties": {}, "title": "debug_mcp_contextArguments", "type": "object"}}, {"name": "debug_sprite_function", "description": "\n    Test specific sprite function execution\n    \n    Args:\n        function_name: Name of sprite function to test\n        **kwargs: Arguments to pass to the sprite function\n        \n    Returns:\n        Dictionary with function execution results\n    ", "input_schema": {"properties": {"function_name": {"default": "list_contexts", "title": "Function Name", "type": "string"}, "kwargs": {"title": "kwargs", "type": "string"}}, "required": ["kwargs"], "title": "debug_sprite_functionArguments", "type": "object"}}, {"name": "deep_research_batch", "description": "\n    Perform batch deep research on multiple queries\n    \n    Args:\n        queries: Dictionary mapping slugs to research queries\n        model: Model to use for research\n        project: Project name for organization\n        parallel: Whether to process in parallel\n        save_individual: Whether to save each result individually\n        \n    Returns:\n        Dictionary mapping slugs to research results\n    ", "input_schema": {"properties": {"queries": {"additionalProperties": {"type": "string"}, "title": "Queries", "type": "object"}, "model": {"default": "o4-mini-deep-research", "title": "Model", "type": "string"}, "project": {"default": "batch_research", "title": "Project", "type": "string"}, "parallel": {"default": true, "title": "<PERSON><PERSON><PERSON>", "type": "boolean"}, "save_individual": {"default": true, "title": "Save Individual", "type": "boolean"}}, "required": ["queries"], "title": "deep_research_batchArguments", "type": "object"}}, {"name": "deep_research_list", "description": "\n    List all saved research in a project\n    \n    Args:\n        project: Project name to list\n        \n    Returns:\n        List of research metadata or error information\n    ", "input_schema": {"properties": {"project": {"default": "general", "title": "Project", "type": "string"}}, "title": "deep_research_listArguments", "type": "object"}}, {"name": "deep_research_load", "description": "\n    Load previously saved research results\n    \n    Args:\n        slug: Research slug to load\n        project: Project name\n        \n    Returns:\n        Loaded research data or error information\n    ", "input_schema": {"properties": {"slug": {"title": "Slug", "type": "string"}, "project": {"default": "general", "title": "Project", "type": "string"}}, "required": ["slug"], "title": "deep_research_loadArguments", "type": "object"}}, {"name": "deep_research_robust", "description": "\n    Perform robust deep research with multi-critic evaluation\n    \n    Args:\n        query: Research question or topic\n        criteria: Evaluation criteria for critics\n        quality_level: Quality level - \"low\", \"medium\", or \"high\"\n        execution_mode: \"sequential\" or \"parallel\" critic evaluation\n        \n    Returns:\n        Research results with evaluation scores and consensus information\n    ", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "criteria": {"default": "Comprehensive, accurate, well-sourced research with clear analysis", "title": "Criteria", "type": "string"}, "quality_level": {"default": "medium", "title": "Quality Level", "type": "string"}, "execution_mode": {"default": "parallel", "title": "Execution Mode", "type": "string"}}, "required": ["query"], "title": "deep_research_robustArguments", "type": "object"}}, {"name": "deep_research_simple", "description": "\n    Perform simple deep research on a query\n    \n    Args:\n        query: Research question or topic\n        model: Model to use for research\n        save_slug: Optional slug for saving results\n        project: Project name for organization\n        use_cache: Whether to use caching\n        \n    Returns:\n        Research results with content, metadata, and sources\n    ", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "model": {"default": "o4-mini-deep-research", "title": "Model", "type": "string"}, "save_slug": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Save Slug"}, "project": {"default": "general", "title": "Project", "type": "string"}, "use_cache": {"default": true, "title": "Use Cache", "type": "boolean"}}, "required": ["query"], "title": "deep_research_simpleArguments", "type": "object"}}, {"name": "delete_rating", "description": "\n    Delete an AsRating record by ID\n    \n    Args:\n        rating_id: ID of the rating to delete\n        \n    Returns:\n        True if deleted successfully, False otherwise\n    ", "input_schema": {"properties": {"rating_id": {"title": "Rating Id", "type": "integer"}}, "required": ["rating_id"], "title": "delete_ratingArguments", "type": "object"}}, {"name": "elf_mem_bank_status", "description": "\n    Get comprehensive status information about a memory bank.\n    \n    Args:\n        bank: Memory bank name\n        \n    Returns:\n        dict: Bank status information\n    ", "input_schema": {"properties": {"bank": {"title": "Bank", "type": "string"}}, "required": ["bank"], "title": "elf_mem_bank_statusArguments", "type": "object"}}, {"name": "elf_mem_delete", "description": "\n    Delete a key from a memory bank.\n    \n    Args:\n        bank: Memory bank name\n        key: Key to delete\n        \n    Returns:\n        dict: Deletion status and metadata\n    ", "input_schema": {"properties": {"bank": {"title": "Bank", "type": "string"}, "key": {"title": "Key", "type": "string"}}, "required": ["bank", "key"], "title": "elf_mem_deleteArguments", "type": "object"}}, {"name": "elf_mem_get", "description": "\n    Retrieve a value from a memory bank with optional type validation.\n    \n    Args:\n        bank: Memory bank name\n        key: Key to retrieve (extension-free)\n        expected_type: Optional type validation:\n            - pd.DataFrame, 'dataframe', 'csv': Expect DataFrame\n            - dict, 'dict', 'json': Expect dictionary  \n            - str, 'str', 'text', 'txt', 'md', 'markdown': Expect string\n        \n    Returns:\n        dict: Retrieved value and metadata, with type validation errors if applicable\n    ", "input_schema": {"properties": {"bank": {"title": "Bank", "type": "string"}, "key": {"title": "Key", "type": "string"}, "expected_type": {"default": null, "title": "expected_type", "type": "string"}}, "required": ["bank", "key"], "title": "elf_mem_getArguments", "type": "object"}}, {"name": "elf_mem_list_banks", "description": "\n    List all available memory banks.\n    \n    Returns:\n        dict: List of all memory banks\n    ", "input_schema": {"properties": {}, "title": "elf_mem_list_banksArguments", "type": "object"}}, {"name": "elf_mem_list_keys", "description": "\n    List keys in a memory bank.\n\n    Args:\n        bank: Memory bank name\n\n    Returns:\n        dict: List of keys and metadata\n    ", "input_schema": {"properties": {"bank": {"title": "Bank", "type": "string"}}, "required": ["bank"], "title": "elf_mem_list_keysArguments", "type": "object"}}, {"name": "elf_mem_store", "description": "\n    Store a value in a memory bank with automatic format detection.\n    \n    Args:\n        bank: Memory bank name (e.g., 'global/system/core', 'project/my_project')\n        key: Key to store the value under (extension-free)\n        value: Value to store - can be:\n            - pandas.DataFrame (stored as CSV)\n            - dict/list (stored as JSON)\n            - str with markdown patterns (stored as .md)\n            - str plain text (stored as .txt)\n        \n    Returns:\n        dict: Success status and metadata\n    ", "input_schema": {"properties": {"bank": {"title": "Bank", "type": "string"}, "key": {"title": "Key", "type": "string"}, "value": {"title": "value", "type": "string"}}, "required": ["bank", "key", "value"], "title": "elf_mem_storeArguments", "type": "object"}}, {"name": "fetch_context", "description": "\n    Fetch a specific AsContext by ID\n    \n    Args:\n        context_id: ID of the context to fetch\n        \n    Returns:\n        Context dictionary or None if not found\n    ", "input_schema": {"properties": {"context_id": {"title": "Context Id", "type": "integer"}}, "required": ["context_id"], "title": "fetch_contextArguments", "type": "object"}}, {"name": "frame_list", "description": "List available parquet data frames", "input_schema": {"properties": {"root_dir": {"default": "/var/lib/gaia/GAIA_FS/frames/", "title": "root_dir", "type": "string"}}, "title": "frame_listArguments", "type": "object"}}, {"name": "frame_search", "description": "One-liner to search data frames with text query", "input_schema": {"properties": {"query": {"default": "Tesla", "title": "query", "type": "string"}, "section_slug": {"default": "agbase", "title": "section_slug", "type": "string"}, "frame_slug": {"default": "agbase_org__slim", "title": "frame_slug", "type": "string"}, "limit": {"default": 100, "title": "limit", "type": "string"}}, "title": "frame_searchArguments", "type": "object"}}, {"name": "investor_search", "description": "One-liner to search for investors with filters", "input_schema": {"properties": {"name": {"default": "<PERSON><PERSON><PERSON>", "title": "name", "type": "string"}, "country": {"default": "SG", "title": "country", "type": "string"}, "investor_type": {"default": "venture vc", "title": "investor_type", "type": "string"}, "min_investments": {"default": 5, "title": "min_investments", "type": "string"}, "limit": {"default": 100, "title": "limit", "type": "string"}}, "title": "investor_searchArguments", "type": "object"}}, {"name": "investor_stats", "description": "Get detailed investor statistics and portfolio analysis", "input_schema": {"properties": {"investor_name": {"default": "Temasek Holdings", "title": "investor_name", "type": "string"}, "country": {"default": "SG", "title": "country", "type": "string"}}, "title": "investor_statsArguments", "type": "object"}}, {"name": "list_contexts", "description": "\n    List AsContext records from legacy database\n\n    Args:\n        limit: Maximum number of records to return\n\n    Returns:\n        List of context dictionaries\n    ", "input_schema": {"properties": {"limit": {"default": 100, "title": "Limit", "type": "integer"}}, "title": "list_contextsArguments", "type": "object"}}, {"name": "list_databases", "description": "\n    List available databases and their descriptions.\n    \n    Returns:\n        dict: Dictionary of available databases with their metadata\n    ", "input_schema": {"properties": {}, "title": "list_databasesArguments", "type": "object"}}, {"name": "list_ratings_for_context", "description": "\n    List all AsRating records for a specific context\n    \n    Args:\n        context_id: ID of the context to get ratings for\n        limit: Maximum number of records to return\n        \n    Returns:\n        List of rating dictionaries\n    ", "input_schema": {"properties": {"context_id": {"title": "Context Id", "type": "integer"}, "limit": {"default": 1000, "title": "Limit", "type": "integer"}}, "required": ["context_id"], "title": "list_ratings_for_contextArguments", "type": "object"}}, {"name": "llm_analyze_code", "description": "Code analysis for CodeAgent", "input_schema": {"properties": {"code": {"title": "Code", "type": "string"}, "language": {"default": "python", "title": "Language", "type": "string"}, "model": {"default": null, "title": "Model", "type": "string"}}, "required": ["code"], "title": "llm_analyze_codeArguments", "type": "object"}}, {"name": "llm_completion_json", "description": "JSON completion for CodeAgent", "input_schema": {"properties": {"prompt": {"title": "Prompt", "type": "string"}, "system_prompt": {"default": null, "title": "System Prompt", "type": "string"}, "schema": {"additionalProperties": true, "default": null, "title": "<PERSON><PERSON><PERSON>", "type": "object"}, "model": {"default": null, "title": "Model", "type": "string"}}, "required": ["prompt"], "title": "llm_completion_jsonArguments", "type": "object"}}, {"name": "llm_completion_text", "description": "Simple text completion for CodeAgent", "input_schema": {"properties": {"prompt": {"title": "Prompt", "type": "string"}, "system_prompt": {"default": null, "title": "System Prompt", "type": "string"}, "model": {"default": null, "title": "Model", "type": "string"}}, "required": ["prompt"], "title": "llm_completion_textArguments", "type": "object"}}, {"name": "llm_debug_error", "description": "Error debugging for CodeAgent", "input_schema": {"properties": {"error_message": {"title": "Error Message", "type": "string"}, "code_context": {"default": "", "title": "Code Context", "type": "string"}, "model": {"default": null, "title": "Model", "type": "string"}}, "required": ["error_message"], "title": "llm_debug_errorArguments", "type": "object"}}, {"name": "llm_explain_concept", "description": "Concept explanation for CodeAgent", "input_schema": {"properties": {"concept": {"title": "Concept", "type": "string"}, "level": {"default": "intermediate", "title": "Level", "type": "string"}, "model": {"default": null, "title": "Model", "type": "string"}}, "required": ["concept"], "title": "llm_explain_conceptArguments", "type": "object"}}, {"name": "llm_generate_code", "description": "Code generation for CodeAgent", "input_schema": {"properties": {"description": {"title": "Description", "type": "string"}, "language": {"default": "python", "title": "Language", "type": "string"}, "style": {"default": "clean", "title": "Style", "type": "string"}, "model": {"default": null, "title": "Model", "type": "string"}}, "required": ["description"], "title": "llm_generate_codeArguments", "type": "object"}}, {"name": "market_research", "description": "One-liner to get market research data (TAM/CAGR) for a sector", "input_schema": {"properties": {"sector": {"default": "AI Agriculture", "title": "sector", "type": "string"}}, "title": "market_researchArguments", "type": "object"}}, {"name": "multimodel_query", "description": "One-liner to query multiple LLM models and get aggregated responses", "input_schema": {"properties": {"prompt": {"default": "List 3 AI agriculture companies", "title": "prompt", "type": "string"}, "models": {"default": null, "title": "models", "type": "string"}, "parallel": {"default": true, "title": "parallel", "type": "string"}, "return_extended": {"default": false, "title": "return_extended", "type": "string"}}, "title": "multimodel_queryArguments", "type": "object"}}, {"name": "news_search", "description": "Search for news articles only", "input_schema": {"properties": {"query": {"default": "AI Agriculture", "title": "query", "type": "string"}, "num_results": {"default": 10, "title": "num_results", "type": "string"}}, "title": "news_searchArguments", "type": "object"}}, {"name": "omni_search", "description": "One-liner to perform omni search across multiple search types", "input_schema": {"properties": {"query": {"default": "deepnote", "title": "query", "type": "string"}, "query_type": {"default": "Query String", "title": "query_type", "type": "string"}, "limit": {"default": 10, "title": "limit", "type": "string"}}, "title": "omni_searchArguments", "type": "object"}}, {"name": "quick_search", "description": "\n    Legacy function for backward compatibility.\n\n    Args:\n        query: Search query string\n\n    Returns:\n        tuple: (results_list, stats_dict)\n    ", "input_schema": {"properties": {"query": {"default": "DevOps Agent", "title": "Query", "type": "string"}}, "title": "quick_searchArguments", "type": "object"}}, {"name": "quick_sql", "description": "\n    Ultra-simple one-liner for quick SQL queries.\n    \n    Args:\n        query (str): Natural language query\n        db (str): Database name (default: agbase_reportdb)\n    \n    Returns:\n        list: Query results as list of dictionaries\n    ", "input_schema": {"properties": {"query": {"title": "query", "type": "string"}, "db": {"default": "agbase_reportdb", "title": "db", "type": "string"}}, "required": ["query"], "title": "quick_sqlArguments", "type": "object"}}, {"name": "search_contexts", "description": "\n    Search AsContext records by name\n    \n    Args:\n        name_query: Search term for context name (case-insensitive contains)\n        limit: Maximum number of records to return\n        \n    Returns:\n        List of matching context dictionaries\n    ", "input_schema": {"properties": {"name_query": {"title": "Name Query", "type": "string"}, "limit": {"default": 50, "title": "Limit", "type": "integer"}}, "required": ["name_query"], "title": "search_contextsArguments", "type": "object"}}, {"name": "simple_chart_png", "description": "\n    Generate a PNG chart file from data table with intelligent chart type selection.\n    \n    Args:\n        data_table: String representation of data table (CSV-like format)\n        chart_spec: Chart specification/requirements (e.g., \"bar chart of sales by region\")\n    \n    Returns:\n        dict: {\n            \"success\": bool,\n            \"png_path\": str,     # Full path to generated PNG file\n            \"error\": str         # Error message if failed\n        }\n    ", "input_schema": {"properties": {"data_table": {"title": "Data Table", "type": "string"}, "chart_spec": {"title": "Chart Spec", "type": "string"}}, "required": ["data_table", "chart_spec"], "title": "simple_chart_pngArguments", "type": "object"}}, {"name": "simple_chart_svg", "description": "\n    Generate an SVG chart from data table with intelligent chart type selection.\n    \n    Args:\n        data_table: String representation of data table (CSV-like format)\n        chart_spec: Chart specification/requirements (e.g., \"bubble chart showing GDP vs population\")\n    \n    Returns:\n        dict: {\n            \"success\": bool,\n            \"svg_content\": str,  # SVG content as string\n            \"error\": str         # Error message if failed\n        }\n    ", "input_schema": {"properties": {"data_table": {"title": "Data Table", "type": "string"}, "chart_spec": {"title": "Chart Spec", "type": "string"}}, "required": ["data_table", "chart_spec"], "title": "simple_chart_svgArguments", "type": "object"}}, {"name": "sql_query", "description": "\n    One-liner to run natural language SQL queries against AgFunder databases.\n    \n    Args:\n        query (str): Natural language query (e.g., \"count rounds from facebook.com\")\n        db (str): Database to query - options: agbase_reportdb, agbasedb, cb (default: agbase_reportdb)\n        execute (bool): Whether to execute the SQL or just generate it (default: True)\n        format_type (str): Output format - dict, json, sql, human, csv (default: dict)\n        model (str): LLM model to use (default: anthropic/claude-sonnet-4-20250514)\n        debug (bool): Enable debug output (default: False)\n    \n    Returns:\n        tuple: (results, metadata) where results is the query results and metadata contains stats\n        \n    Examples:\n        # Basic query\n        results, stats = sql_query(\"count rounds from facebook.com\")\n        \n        # Query different database\n        results, stats = sql_query(\"find AI companies\", db=\"cb\")\n        \n        # Generate SQL only (don't execute)\n        results, stats = sql_query(\"show investors\", execute=False)\n        \n        # Get raw SQL\n        results, stats = sql_query(\"count organizations\", format_type=\"sql\")\n\n        # Get CSV format\n        results, stats = sql_query(\"show top companies\", format_type=\"csv\")\n    ", "input_schema": {"properties": {"query": {"title": "query", "type": "string"}, "db": {"default": "agbase_reportdb", "title": "db", "type": "string"}, "execute": {"default": true, "title": "execute", "type": "string"}, "format_type": {"default": "dict", "title": "format_type", "type": "string"}, "model": {"default": null, "title": "model", "type": "string"}, "debug": {"default": false, "title": "debug", "type": "string"}}, "required": ["query"], "title": "sql_queryArguments", "type": "object"}}, {"name": "web_search", "description": "One-liner to perform web search using GaiaMultiSearch", "input_schema": {"properties": {"query": {"default": "AI Agriculture", "title": "query", "type": "string"}, "sources": {"default": ["web"], "title": "sources", "type": "string"}, "num_results": {"default": 10, "title": "num_results", "type": "string"}}, "title": "web_searchArguments", "type": "object"}}, {"name": "wiki_search", "description": "One-liner to search Wikipedia and get summary", "input_schema": {"properties": {"query": {"default": "Apple Inc", "title": "query", "type": "string"}, "sentences": {"default": 3, "title": "sentences", "type": "string"}}, "title": "wiki_searchArguments", "type": "object"}}, {"name": "exa__web_search_exa", "description": "Search the web using Exa AI - performs real-time web searches and can scrape content from specific URLs. Supports configurable result counts and returns the content from the most relevant websites.", "input_schema": {"properties": {"query": {"title": "query", "type": "string"}, "numResults": {"default": null, "title": "numResults", "type": "string"}}, "required": ["query"], "title": "exa__web_search_exaArguments", "type": "object"}}, {"name": "exa__research_paper_search_exa", "description": "Search for academic papers and research using Exa AI - specializes in finding scholarly articles, research papers, and academic content. Returns detailed information about research findings and academic sources.", "input_schema": {"properties": {"query": {"title": "query", "type": "string"}, "numResults": {"default": null, "title": "numResults", "type": "string"}}, "required": ["query"], "title": "exa__research_paper_search_exaArguments", "type": "object"}}, {"name": "exa__company_research_exa", "description": "Research companies using Exa AI - finds comprehensive information about businesses, organizations, and corporations. Provides insights into company operations, news, financial information, and industry analysis.", "input_schema": {"properties": {"companyName": {"title": "companyName", "type": "string"}, "numResults": {"default": null, "title": "numResults", "type": "string"}}, "required": ["companyName"], "title": "exa__company_research_exaArguments", "type": "object"}}, {"name": "exa__crawling_exa", "description": "Extract and crawl content from specific URLs using Exa AI - retrieves full text content, metadata, and structured information from web pages. Ideal for extracting detailed content from known URLs.", "input_schema": {"properties": {"url": {"title": "url", "type": "string"}, "maxCharacters": {"default": null, "title": "maxCharacters", "type": "string"}}, "required": ["url"], "title": "exa__crawling_exaArguments", "type": "object"}}, {"name": "exa__competitor_finder_exa", "description": "Find competitors for a business using Exa AI - identifies similar companies, competitive landscape analysis, and market positioning. Helps discover direct and indirect competitors in any industry.", "input_schema": {"properties": {"companyName": {"title": "companyName", "type": "string"}, "industry": {"default": null, "title": "industry", "type": "string"}, "numResults": {"default": null, "title": "numResults", "type": "string"}}, "required": ["companyName"], "title": "exa__competitor_finder_exaArguments", "type": "object"}}, {"name": "exa__linkedin_search_exa", "description": "Search LinkedIn profiles and companies using Exa AI - finds professional profiles, company pages, and business-related content on LinkedIn. Useful for networking, recruitment, and business research.", "input_schema": {"properties": {"query": {"title": "query", "type": "string"}, "searchType": {"default": null, "title": "searchType", "type": "string"}, "numResults": {"default": null, "title": "numResults", "type": "string"}}, "required": ["query"], "title": "exa__linkedin_search_exaArguments", "type": "object"}}, {"name": "exa__wikipedia_search_exa", "description": "Search Wikipedia articles using Exa AI - finds comprehensive, factual information from Wikipedia entries. Ideal for research, fact-checking, and getting authoritative information on various topics.", "input_schema": {"properties": {"query": {"title": "query", "type": "string"}, "numResults": {"default": null, "title": "numResults", "type": "string"}}, "required": ["query"], "title": "exa__wikipedia_search_exaArguments", "type": "object"}}, {"name": "exa__github_search_exa", "description": "Search GitHub repositories and code using Exa AI - finds repositories, code snippets, documentation, and developer profiles on GitHub. Useful for finding open source projects, code examples, and technical resources.", "input_schema": {"properties": {"query": {"title": "query", "type": "string"}, "searchType": {"default": null, "title": "searchType", "type": "string"}, "numResults": {"default": null, "title": "numResults", "type": "string"}}, "required": ["query"], "title": "exa__github_search_exaArguments", "type": "object"}}, {"name": "exa__deep_researcher_start", "description": "Start a comprehensive AI-powered deep research task for complex queries. This tool initiates an intelligent agent that performs extensive web searches, crawls relevant pages, analyzes information, and synthesizes findings into a detailed research report. The agent thinks critically about the research topic and provides thorough, well-sourced answers. Use this for complex research questions that require in-depth analysis rather than simple searches. After starting a research task, IMMEDIATELY use deep_researcher_check with the returned task ID to monitor progress and retrieve results.", "input_schema": {"properties": {"instructions": {"title": "instructions", "type": "string"}, "model": {"default": null, "title": "model", "type": "string"}}, "required": ["instructions"], "title": "exa__deep_researcher_startArguments", "type": "object"}}, {"name": "exa__deep_researcher_check", "description": "Check the status and retrieve results of a deep research task. This tool monitors the progress of an AI agent that performs comprehensive web searches, analyzes multiple sources, and synthesizes findings into detailed research reports. The tool includes a built-in 5-second delay before checking to allow processing time. IMPORTANT: You must call this tool repeatedly (poll) until the status becomes 'completed' to get the final research results. When status is 'running', wait a few seconds and call this tool again with the same task ID.", "input_schema": {"properties": {"taskId": {"title": "taskId", "type": "string"}}, "required": ["taskId"], "title": "exa__deep_researcher_checkArguments", "type": "object"}}, {"name": "fc__firecrawl_scrape", "description": "\nScrape content from a single URL with advanced options. \nThis is the most powerful, fastest and most reliable scraper tool, if available you should always default to using this tool for any web scraping needs.\n\n**Best for:** Single page content extraction, when you know exactly which page contains the information.\n**Not recommended for:** Multiple pages (use batch_scrape), unknown page (use search), structured data (use extract).\n**Common mistakes:** Using scrape for a list of URLs (use batch_scrape instead). If batch scrape doesnt work, just use scrape and call it multiple times.\n**Prompt Example:** \"Get the content of the page at https://example.com.\"\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_scrape\",\n  \"arguments\": {\n    \"url\": \"https://example.com\",\n    \"formats\": [\"markdown\"],\n    \"maxAge\": 3600000\n  }\n}\n```\n**Performance:** Add maxAge parameter for 500% faster scrapes using cached data.\n**Returns:** Markdown, HTML, or other formats as specified.\n", "input_schema": {"properties": {"url": {"title": "url", "type": "string"}, "formats": {"default": null, "title": "formats", "type": "string"}, "onlyMainContent": {"default": null, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string"}, "includeTags": {"default": null, "title": "includeTags", "type": "string"}, "excludeTags": {"default": null, "title": "excludeTags", "type": "string"}, "waitFor": {"default": null, "title": "waitFor", "type": "string"}, "timeout": {"default": null, "title": "timeout", "type": "string"}, "actions": {"default": null, "title": "actions", "type": "string"}, "extract": {"default": null, "title": "extract", "type": "string"}, "mobile": {"default": null, "title": "mobile", "type": "string"}, "skipTlsVerification": {"default": null, "title": "skipTlsVerification", "type": "string"}, "removeBase64Images": {"default": null, "title": "removeBase64Images", "type": "string"}, "location": {"default": null, "title": "location", "type": "string"}, "maxAge": {"default": null, "title": "maxAge", "type": "string"}}, "required": ["url"], "title": "fc__firecrawl_scrapeArguments", "type": "object"}}, {"name": "fc__firecrawl_map", "description": "\nMap a website to discover all indexed URLs on the site.\n\n**Best for:** Discovering URLs on a website before deciding what to scrape; finding specific sections of a website.\n**Not recommended for:** When you already know which specific URL you need (use scrape or batch_scrape); when you need the content of the pages (use scrape after mapping).\n**Common mistakes:** Using crawl to discover URLs instead of map.\n**Prompt Example:** \"List all URLs on example.com.\"\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_map\",\n  \"arguments\": {\n    \"url\": \"https://example.com\"\n  }\n}\n```\n**Returns:** Array of URLs found on the site.\n", "input_schema": {"properties": {"url": {"title": "url", "type": "string"}, "search": {"default": null, "title": "search", "type": "string"}, "ignoreSitemap": {"default": null, "title": "ignoreSitemap", "type": "string"}, "sitemapOnly": {"default": null, "title": "sitemapOnly", "type": "string"}, "includeSubdomains": {"default": null, "title": "includeSubdomains", "type": "string"}, "limit": {"default": null, "title": "limit", "type": "string"}}, "required": ["url"], "title": "fc__firecrawl_mapArguments", "type": "object"}}, {"name": "fc__firecrawl_crawl", "description": "\nStarts an asynchronous crawl job on a website and extracts content from all pages.\n\n**Best for:** Extracting content from multiple related pages, when you need comprehensive coverage.\n**Not recommended for:** Extracting content from a single page (use scrape); when token limits are a concern (use map + batch_scrape); when you need fast results (crawling can be slow).\n**Warning:** Crawl responses can be very large and may exceed token limits. Limit the crawl depth and number of pages, or use map + batch_scrape for better control.\n**Common mistakes:** Setting limit or maxDepth too high (causes token overflow); using crawl for a single page (use scrape instead).\n**Prompt Example:** \"Get all blog posts from the first two levels of example.com/blog.\"\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_crawl\",\n  \"arguments\": {\n    \"url\": \"https://example.com/blog/*\",\n    \"maxDepth\": 2,\n    \"limit\": 100,\n    \"allowExternalLinks\": false,\n    \"deduplicateSimilarURLs\": true\n  }\n}\n```\n**Returns:** Operation ID for status checking; use firecrawl_check_crawl_status to check progress.\n", "input_schema": {"properties": {"url": {"title": "url", "type": "string"}, "excludePaths": {"default": null, "title": "excludePaths", "type": "string"}, "includePaths": {"default": null, "title": "includePaths", "type": "string"}, "maxDepth": {"default": null, "title": "max<PERSON><PERSON><PERSON>", "type": "string"}, "ignoreSitemap": {"default": null, "title": "ignoreSitemap", "type": "string"}, "limit": {"default": null, "title": "limit", "type": "string"}, "allowBackwardLinks": {"default": null, "title": "allowBackwardLinks", "type": "string"}, "allowExternalLinks": {"default": null, "title": "allowExternalLinks", "type": "string"}, "webhook": {"default": null, "title": "webhook", "type": "string"}, "deduplicateSimilarURLs": {"default": null, "title": "deduplicateSimilarURLs", "type": "string"}, "ignoreQueryParameters": {"default": null, "title": "ignoreQueryParameters", "type": "string"}, "scrapeOptions": {"default": null, "title": "scrapeOptions", "type": "string"}}, "required": ["url"], "title": "fc__firecrawl_crawlArguments", "type": "object"}}, {"name": "fc__firecrawl_check_crawl_status", "description": "\nCheck the status of a crawl job.\n\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_check_crawl_status\",\n  \"arguments\": {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440000\"\n  }\n}\n```\n**Returns:** Status and progress of the crawl job, including results if available.\n", "input_schema": {"properties": {"id": {"title": "id", "type": "string"}}, "required": ["id"], "title": "fc__firecrawl_check_crawl_statusArguments", "type": "object"}}, {"name": "fc__firecrawl_search", "description": "\nSearch the web and optionally extract content from search results. This is the most powerful search tool available, and if available you should always default to using this tool for any web search needs.\n\n**Best for:** Finding specific information across multiple websites, when you don't know which website has the information; when you need the most relevant content for a query.\n**Not recommended for:** When you already know which website to scrape (use scrape); when you need comprehensive coverage of a single website (use map or crawl).\n**Common mistakes:** Using crawl or map for open-ended questions (use search instead).\n**Prompt Example:** \"Find the latest research papers on AI published in 2023.\"\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_search\",\n  \"arguments\": {\n    \"query\": \"latest AI research papers 2023\",\n    \"limit\": 5,\n    \"lang\": \"en\",\n    \"country\": \"us\",\n    \"scrapeOptions\": {\n      \"formats\": [\"markdown\"],\n      \"onlyMainContent\": true\n    }\n  }\n}\n```\n**Returns:** Array of search results (with optional scraped content).\n", "input_schema": {"properties": {"query": {"title": "query", "type": "string"}, "limit": {"default": null, "title": "limit", "type": "string"}, "lang": {"default": null, "title": "lang", "type": "string"}, "country": {"default": null, "title": "country", "type": "string"}, "tbs": {"default": null, "title": "tbs", "type": "string"}, "filter": {"default": null, "title": "filter", "type": "string"}, "location": {"default": null, "title": "location", "type": "string"}, "scrapeOptions": {"default": null, "title": "scrapeOptions", "type": "string"}}, "required": ["query"], "title": "fc__firecrawl_searchArguments", "type": "object"}}, {"name": "fc__firecrawl_extract", "description": "\nExtract structured information from web pages using LLM capabilities. Supports both cloud AI and self-hosted LLM extraction.\n\n**Best for:** Extracting specific structured data like prices, names, details.\n**Not recommended for:** When you need the full content of a page (use scrape); when you're not looking for specific structured data.\n**Arguments:**\n- urls: Array of URLs to extract information from\n- prompt: Custom prompt for the LLM extraction\n- systemPrompt: System prompt to guide the LLM\n- schema: JSON schema for structured data extraction\n- allowExternalLinks: Allow extraction from external links\n- enableWebSearch: Enable web search for additional context\n- includeSubdomains: Include subdomains in extraction\n**Prompt Example:** \"Extract the product name, price, and description from these product pages.\"\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_extract\",\n  \"arguments\": {\n    \"urls\": [\"https://example.com/page1\", \"https://example.com/page2\"],\n    \"prompt\": \"Extract product information including name, price, and description\",\n    \"systemPrompt\": \"You are a helpful assistant that extracts product information\",\n    \"schema\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"name\": { \"type\": \"string\" },\n        \"price\": { \"type\": \"number\" },\n        \"description\": { \"type\": \"string\" }\n      },\n      \"required\": [\"name\", \"price\"]\n    },\n    \"allowExternalLinks\": false,\n    \"enableWebSearch\": false,\n    \"includeSubdomains\": false\n  }\n}\n```\n**Returns:** Extracted structured data as defined by your schema.\n", "input_schema": {"properties": {"urls": {"title": "urls", "type": "string"}, "prompt": {"default": null, "title": "prompt", "type": "string"}, "systemPrompt": {"default": null, "title": "systemPrompt", "type": "string"}, "schema": {"default": null, "title": "schema", "type": "string"}, "allowExternalLinks": {"default": null, "title": "allowExternalLinks", "type": "string"}, "enableWebSearch": {"default": null, "title": "enableWebSearch", "type": "string"}, "includeSubdomains": {"default": null, "title": "includeSubdomains", "type": "string"}}, "required": ["urls"], "title": "fc__firecrawl_extractArguments", "type": "object"}}, {"name": "fc__firecrawl_deep_research", "description": "\nConduct deep web research on a query using intelligent crawling, search, and LLM analysis.\n\n**Best for:** Complex research questions requiring multiple sources, in-depth analysis.\n**Not recommended for:** Simple questions that can be answered with a single search; when you need very specific information from a known page (use scrape); when you need results quickly (deep research can take time).\n**Arguments:**\n- query (string, required): The research question or topic to explore.\n- maxDepth (number, optional): Maximum recursive depth for crawling/search (default: 3).\n- timeLimit (number, optional): Time limit in seconds for the research session (default: 120).\n- maxUrls (number, optional): Maximum number of URLs to analyze (default: 50).\n**Prompt Example:** \"Research the environmental impact of electric vehicles versus gasoline vehicles.\"\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_deep_research\",\n  \"arguments\": {\n    \"query\": \"What are the environmental impacts of electric vehicles compared to gasoline vehicles?\",\n    \"maxDepth\": 3,\n    \"timeLimit\": 120,\n    \"maxUrls\": 50\n  }\n}\n```\n**Returns:** Final analysis generated by an LLM based on research. (data.finalAnalysis); may also include structured activities and sources used in the research process.\n", "input_schema": {"properties": {"query": {"title": "query", "type": "string"}, "maxDepth": {"default": null, "title": "max<PERSON><PERSON><PERSON>", "type": "string"}, "timeLimit": {"default": null, "title": "timeLimit", "type": "string"}, "maxUrls": {"default": null, "title": "maxUrls", "type": "string"}}, "required": ["query"], "title": "fc__firecrawl_deep_researchArguments", "type": "object"}}, {"name": "fc__firecrawl_generate_llmstxt", "description": "\nGenerate a standardized llms.txt (and optionally llms-full.txt) file for a given domain. This file defines how large language models should interact with the site.\n\n**Best for:** Creating machine-readable permission guidelines for AI models.\n**Not recommended for:** General content extraction or research.\n**Arguments:**\n- url (string, required): The base URL of the website to analyze.\n- maxUrls (number, optional): Max number of URLs to include (default: 10).\n- showFullText (boolean, optional): Whether to include llms-full.txt contents in the response.\n**Prompt Example:** \"Generate an LLMs.txt file for example.com.\"\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_generate_llmstxt\",\n  \"arguments\": {\n    \"url\": \"https://example.com\",\n    \"maxUrls\": 20,\n    \"showFullText\": true\n  }\n}\n```\n**Returns:** LLMs.txt file contents (and optionally llms-full.txt).\n", "input_schema": {"properties": {"url": {"title": "url", "type": "string"}, "maxUrls": {"default": null, "title": "maxUrls", "type": "string"}, "showFullText": {"default": null, "title": "showFullText", "type": "string"}}, "required": ["url"], "title": "fc__firecrawl_generate_llmstxtArguments", "type": "object"}}], "max_tokens": 1024, "temperature": null, "system": null}, "response": {"content": null, "tool_calls": null, "finish_reason": null, "raw_response": null}, "token_usage": {}, "error": "Unknown field for <PERSON><PERSON><PERSON>: title", "metadata": {"message_count": 13, "tool_count": 67, "has_system_prompt": false, "response_has_tool_calls": false}}