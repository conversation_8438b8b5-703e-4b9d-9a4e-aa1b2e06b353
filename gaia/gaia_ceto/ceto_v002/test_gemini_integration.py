#!/usr/bin/env python3
"""
Test script to verify Gemini integration with MCP tools.
"""

import os
import sys

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

def test_gemini_import():
    """Test if Google Generative AI library is available."""
    print("=" * 60)
    print("Testing Gemini Library Import")
    print("=" * 60)
    
    try:
        import google.generativeai as genai
        print("✅ Google Generative AI library is available!")
        print(f"   Library version: {getattr(genai, '__version__', 'Unknown')}")
        return True
    except ImportError as e:
        print("❌ Google Generative AI library not available.")
        print(f"   Error: {e}")
        print("\n📦 To install, run:")
        print("   pip install google-generativeai")
        return False


def test_gemini_client_init():
    """Test Gemini client initialization."""
    print("\n" + "=" * 60)
    print("Testing Gemini Client Initialization")
    print("=" * 60)
    
    try:
        # Mock the mcp import to avoid the import error
        import unittest.mock
        
        with unittest.mock.patch('gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib.ClientSession'), \
             unittest.mock.patch('gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib.types'), \
             unittest.mock.patch('gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib.streamablehttp_client'):
            
            from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib
            
            # Test Gemini initialization
            print("🔧 Creating Gemini MCP client...")
            client = MCPClientLib(llm_provider="gemini")
            
            print("✅ MCPClientLib created successfully!")
            print(f"   LLM Provider: {client.llm_provider}")
            print(f"   Anthropic Client: {'Available' if hasattr(client, 'anthropic') else 'Missing'}")
            print(f"   OpenAI Client: {'Available' if hasattr(client, 'openai') else 'Missing'}")
            print(f"   Gemini Client: {'Available' if hasattr(client, 'gemini') else 'Missing'}")
            
            if hasattr(client, 'gemini'):
                print(f"   Gemini Value: {client.gemini}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gemini_api_key():
    """Test Gemini API key configuration."""
    print("\n" + "=" * 60)
    print("Testing Gemini API Key Configuration")
    print("=" * 60)
    
    # Check for API key in environment
    gemini_key = os.environ.get("GEMINI_API_KEY")
    google_key = os.environ.get("GOOGLE_API_KEY")
    
    if gemini_key:
        print("✅ GEMINI_API_KEY found in environment")
        print(f"   Key starts with: {gemini_key[:10]}...")
        return True
    elif google_key:
        print("✅ GOOGLE_API_KEY found in environment")
        print(f"   Key starts with: {google_key[:10]}...")
        return True
    else:
        print("❌ No Gemini API key found in environment")
        print("\n🔑 To set up your API key:")
        print("   1. Get an API key from: https://makersuite.google.com/app/apikey")
        print("   2. Set environment variable:")
        print("      export GEMINI_API_KEY='your-api-key-here'")
        print("   OR")
        print("      export GOOGLE_API_KEY='your-api-key-here'")
        return False


def show_usage_instructions():
    """Show how to use Gemini with Chaterm."""
    print("\n" + "=" * 60)
    print("How to Use Gemini with Chaterm")
    print("=" * 60)
    
    print("\n🚀 Start the MCP server (in one terminal):")
    print("cd /home/<USER>/django-projects/agbase_admin")
    print("python gaia/gaia_ceto/proto_mcp_http/mcp_http_server_multi.py")
    
    print("\n🤖 Run Chaterm with Gemini (in another terminal):")
    print("cd /home/<USER>/django-projects/agbase_admin")
    print("export GEMINI_API_KEY='your-api-key-here'")
    print("python gaia/gaia_ceto/ceto_v002/chat_term.py \\")
    print("  --llm mcp-http \\")
    print("  --mcp-llm-provider gemini \\")
    print("  --model gemini-2.0-flash-exp")
    
    print("\n🎯 Available Gemini Models:")
    print("• gemini-2.0-flash-exp (Latest experimental)")
    print("• gemini-1.5-pro")
    print("• gemini-1.5-flash")
    
    print("\n🛠️  Test Commands:")
    print("• get_keys - List memory banks")
    print("• search 'your query' - Search the web")
    print("• help - Show all available commands")


def main():
    """Main test function."""
    print("🚀 Testing Gemini Integration with MCP Tools\n")
    
    # Test library import
    import_success = test_gemini_import()
    
    if not import_success:
        print("\n❌ Cannot proceed without Google Generative AI library.")
        return False
    
    # Test client initialization
    client_success = test_gemini_client_init()
    
    # Test API key configuration
    api_key_success = test_gemini_api_key()
    
    # Show usage instructions
    show_usage_instructions()
    
    if client_success:
        print("\n" + "=" * 60)
        print("✅ Gemini Integration Ready!")
        print("=" * 60)
        
        if api_key_success:
            print("\n🎉 You can now use Gemini with MCP tools!")
            print("Follow the usage instructions above.")
        else:
            print("\n⚠️  Set up your API key first, then you're ready to go!")
        
        return True
    else:
        print("\n❌ Gemini integration has issues.")
        print("Check the error messages above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
