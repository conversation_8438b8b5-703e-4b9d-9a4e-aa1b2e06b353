#!/usr/bin/env python3
"""
Test script to verify MCPClientLib initialization.
"""

import os
import sys

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

def test_anthropic_init():
    """Test Anthropic client initialization."""
    print("=" * 60)
    print("Testing Anthropic Client Initialization")
    print("=" * 60)
    
    try:
        # Mock the mcp import to avoid the import error
        import unittest.mock
        
        with unittest.mock.patch('gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib.ClientSession'), \
             unittest.mock.patch('gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib.types'), \
             unittest.mock.patch('gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib.streamablehttp_client'):
            
            from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MC<PERSON>lient<PERSON>ib
            
            # Test Anthropic initialization
            print("🔧 Creating Anthropic MCP client...")
            client = MCPClientLib(llm_provider="anthropic")
            
            print("✅ MCPClientLib created successfully!")
            print(f"   LLM Provider: {client.llm_provider}")
            print(f"   Anthropic Client: {'Available' if hasattr(client, 'anthropic') else 'Missing'}")
            print(f"   OpenAI Client: {'Available' if hasattr(client, 'openai') else 'Missing'}")
            
            if hasattr(client, 'anthropic'):
                print(f"   Anthropic Value: {client.anthropic}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_openai_init():
    """Test OpenAI client initialization."""
    print("\n" + "=" * 60)
    print("Testing OpenAI Client Initialization")
    print("=" * 60)
    
    try:
        # Mock the mcp import to avoid the import error
        import unittest.mock
        
        with unittest.mock.patch('gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib.ClientSession'), \
             unittest.mock.patch('gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib.types'), \
             unittest.mock.patch('gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib.streamablehttp_client'):
            
            from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib
            
            # Test OpenAI initialization
            print("🔧 Creating OpenAI MCP client...")
            client = MCPClientLib(llm_provider="openai")
            
            print("✅ MCPClientLib created successfully!")
            print(f"   LLM Provider: {client.llm_provider}")
            print(f"   Anthropic Client: {'Available' if hasattr(client, 'anthropic') else 'Missing'}")
            print(f"   OpenAI Client: {'Available' if hasattr(client, 'openai') else 'Missing'}")
            
            if hasattr(client, 'openai'):
                print(f"   OpenAI Value: {client.openai}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_conversation_id():
    """Test conversation ID functionality."""
    print("\n" + "=" * 60)
    print("Testing Conversation ID")
    print("=" * 60)
    
    try:
        import unittest.mock
        
        with unittest.mock.patch('gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib.ClientSession'), \
             unittest.mock.patch('gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib.types'), \
             unittest.mock.patch('gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib.streamablehttp_client'):
            
            from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib
            
            # Test conversation ID
            print("🔧 Creating client with conversation ID...")
            client = MCPClientLib(llm_provider="anthropic", conversation_id="test-123")
            
            print("✅ Client created with conversation ID!")
            print(f"   Conversation ID: {client.conversation_id}")
            
            # Test setting conversation ID
            client.set_conversation_id("new-456")
            print(f"   Updated Conversation ID: {client.conversation_id}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 Testing MCPClientLib Initialization\n")
    
    anthropic_success = test_anthropic_init()
    openai_success = test_openai_init()
    conversation_success = test_conversation_id()
    
    if anthropic_success and openai_success and conversation_success:
        print("\n" + "=" * 60)
        print("✅ All initialization tests passed!")
        print("=" * 60)
        print("\nThe MCPClientLib initialization fix should work.")
        print("You can now try running Chaterm again:")
        print("python gaia/gaia_ceto/ceto_v002/chat_term.py \\")
        print("  --llm mcp-http \\")
        print("  --mcp-llm-provider openai \\")
        print("  --model gpt-4o-mini")
    else:
        print("\n❌ Some tests failed!")
        print("There may still be issues with the initialization.")
