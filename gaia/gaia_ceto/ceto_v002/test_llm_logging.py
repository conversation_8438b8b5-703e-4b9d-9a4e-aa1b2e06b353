#!/usr/bin/env python3
"""
Test script to verify LLM call logging functionality.
"""

import os
import json
import glob
from datetime import datetime

def test_llm_logging():
    """Test the LLM logging functionality."""
    print("=" * 60)
    print("Testing LLM Call Logging")
    print("=" * 60)
    
    # Check if log directory exists
    log_dir = "llm_call_logs"
    if not os.path.exists(log_dir):
        print(f"❌ Log directory '{log_dir}' does not exist yet.")
        print("Run Chaterm with OpenAI to generate logs first.")
        return False
    
    # Find recent log files
    log_files = glob.glob(os.path.join(log_dir, "*.json"))
    if not log_files:
        print(f"❌ No log files found in '{log_dir}'.")
        print("Run Chaterm with OpenAI to generate logs first.")
        return False
    
    # Sort by modification time (newest first)
    log_files.sort(key=os.path.getmtime, reverse=True)
    
    print(f"✅ Found {len(log_files)} log files")
    
    # Analyze the most recent log files
    for i, log_file in enumerate(log_files[:3]):  # Show last 3 files
        print(f"\n📄 Log file {i+1}: {os.path.basename(log_file)}")
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)
            
            # Display key information
            print(f"   🕒 Timestamp: {log_data.get('timestamp', 'N/A')}")
            print(f"   🤖 Provider: {log_data.get('provider', 'N/A')}")
            print(f"   📞 Call Type: {log_data.get('call_type', 'N/A')}")
            print(f"   ⏱️  Latency: {log_data.get('latency_seconds', 0):.3f}s")
            print(f"   🔧 Model: {log_data.get('request', {}).get('model', 'N/A')}")
            
            # Token usage
            token_usage = log_data.get('token_usage', {})
            if token_usage:
                print(f"   🎯 Tokens: {token_usage}")
            
            # Metadata
            metadata = log_data.get('metadata', {})
            print(f"   📊 Messages: {metadata.get('message_count', 0)}")
            print(f"   🛠️  Tools: {metadata.get('tool_count', 0)}")
            print(f"   🔧 Has Tool Calls: {metadata.get('response_has_tool_calls', False)}")
            
            # Error status
            if log_data.get('error'):
                print(f"   ❌ Error: {log_data['error']}")
            else:
                print(f"   ✅ Success")
                
        except Exception as e:
            print(f"   ❌ Error reading log file: {e}")
    
    return True


def analyze_token_usage():
    """Analyze token usage patterns from logs."""
    print("\n" + "=" * 60)
    print("Token Usage Analysis")
    print("=" * 60)
    
    log_dir = "llm_call_logs"
    log_files = glob.glob(os.path.join(log_dir, "*.json"))
    
    if not log_files:
        print("❌ No log files found for analysis.")
        return
    
    total_calls = 0
    total_tokens = 0
    provider_stats = {}
    call_type_stats = {}
    
    for log_file in log_files:
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)
            
            provider = log_data.get('provider', 'unknown')
            call_type = log_data.get('call_type', 'unknown')
            token_usage = log_data.get('token_usage', {})
            
            total_calls += 1
            
            # Count tokens (handle different provider formats)
            tokens = 0
            if 'total_tokens' in token_usage:
                tokens = token_usage['total_tokens']
            elif 'input_tokens' in token_usage and 'output_tokens' in token_usage:
                tokens = token_usage['input_tokens'] + token_usage['output_tokens']
            
            total_tokens += tokens
            
            # Provider stats
            if provider not in provider_stats:
                provider_stats[provider] = {'calls': 0, 'tokens': 0}
            provider_stats[provider]['calls'] += 1
            provider_stats[provider]['tokens'] += tokens
            
            # Call type stats
            if call_type not in call_type_stats:
                call_type_stats[call_type] = {'calls': 0, 'tokens': 0}
            call_type_stats[call_type]['calls'] += 1
            call_type_stats[call_type]['tokens'] += tokens
            
        except Exception as e:
            print(f"❌ Error processing {log_file}: {e}")
    
    print(f"📊 Total API Calls: {total_calls}")
    print(f"🎯 Total Tokens: {total_tokens:,}")
    print(f"📈 Average Tokens per Call: {total_tokens/total_calls:.1f}" if total_calls > 0 else "")
    
    print(f"\n📱 By Provider:")
    for provider, stats in provider_stats.items():
        avg_tokens = stats['tokens'] / stats['calls'] if stats['calls'] > 0 else 0
        print(f"   {provider}: {stats['calls']} calls, {stats['tokens']:,} tokens (avg: {avg_tokens:.1f})")
    
    print(f"\n📞 By Call Type:")
    for call_type, stats in call_type_stats.items():
        avg_tokens = stats['tokens'] / stats['calls'] if stats['calls'] > 0 else 0
        print(f"   {call_type}: {stats['calls']} calls, {stats['tokens']:,} tokens (avg: {avg_tokens:.1f})")


if __name__ == "__main__":
    print("🚀 Testing LLM Call Logging\n")
    
    success = test_llm_logging()
    
    if success:
        analyze_token_usage()
        
        print("\n" + "=" * 60)
        print("✅ LLM logging is working!")
        print("=" * 60)
        print("\nLog files contain:")
        print("• Complete request/response data")
        print("• Token usage information")
        print("• Latency measurements")
        print("• Error information (if any)")
        print("• Conversation context")
        print("\nUse these logs to:")
        print("• Debug API issues")
        print("• Analyze token usage patterns")
        print("• Optimize performance")
        print("• Track conversation costs")
    else:
        print("\n❌ No logs found yet.")
        print("Run Chaterm with OpenAI to generate logs:")
        print("python gaia/gaia_ceto/ceto_v002/chat_term.py \\")
        print("  --llm mcp-http \\")
        print("  --mcp-llm-provider openai \\")
        print("  --model gpt-4o-mini")
