"""
MCP HTTP Client Library

This module provides a reusable client library for interacting with MCP servers via streamable HTTP.
It handles the core functionality of connecting to an MCP server, calling tools,
and processing queries with <PERSON>.
"""

import asyncio
import json
import os
import time
import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any, Callable, Union
from contextlib import AsyncExitStack
import logging

# Import ClientSession and types from mcp core
from mcp import ClientSession, types
# Import streamablehttp_client specifically for HTTP transport
from mcp.client.streamable_http import streamablehttp_client

from anthropic import Anthropic
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

# Configure logging
logger = logging.getLogger(__name__)


class LatencyTracker:
    """Track latency for different parts of the processing chain."""

    def __init__(self):
        self.timings = {}
        self.start_times = {}
        self.total_start_time = None

    def start_total(self):
        """Start tracking total processing time."""
        self.total_start_time = time.time()

    def start_step(self, step_name: str):
        """Start timing a specific step."""
        self.start_times[step_name] = time.time()

    def end_step(self, step_name: str):
        """End timing a specific step."""
        if step_name in self.start_times:
            duration = time.time() - self.start_times[step_name]
            self.timings[step_name] = duration
            del self.start_times[step_name]
            return duration
        return 0

    def get_total_time(self) -> float:
        """Get total processing time."""
        if self.total_start_time:
            return time.time() - self.total_start_time
        return 0

    def print_summary(self, prefix: str = ""):
        """Print a summary of all timings."""
        total_time = self.get_total_time()

        print(f"\n{prefix}{'=' * 50}")
        print(f"{prefix}⏱️  MCP DETAILED LATENCY")
        print(f"{prefix}{'=' * 50}")

        if self.timings:
            for step, duration in self.timings.items():
                percentage = (duration / total_time * 100) if total_time > 0 else 0
                print(f"{prefix}{step:<25}: {duration:6.3f}s ({percentage:5.1f}%)")

        print(f"{prefix}{'-' * 50}")
        print(f"{prefix}{'MCP TOTAL TIME':<25}: {total_time:6.3f}s (100.0%)")
        print(f"{prefix}{'=' * 50}")

    def reset(self):
        """Reset all timings."""
        self.timings.clear()
        self.start_times.clear()
        self.total_start_time = None


# --- Configuration ---
DEFAULT_SERVER_URL = "http://0.0.0.0:9000/mcp"  # Default URL for HTTP
# Import default model from centralized config
try:
    from gaia.gaia_llm.model_config import DEFAULT_ANTHROPIC_MODEL
    DEFAULT_MODEL = DEFAULT_ANTHROPIC_MODEL
except ImportError:
    DEFAULT_MODEL = "claude-3-5-sonnet-20241022"  # Fallback if import fails


class LLMCallLogger:
    """Logger for LLM API calls with detailed request/response information."""

    def __init__(self, log_dir: str = "llm_call_logs"):
        self.log_dir = log_dir
        self.ensure_log_dir()

    def ensure_log_dir(self):
        """Ensure the log directory exists."""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir, exist_ok=True)

    def log_call(self,
                 provider: str,
                 call_type: str,  # "initial" or "followup"
                 request_data: Dict[str, Any],
                 response_data: Dict[str, Any],
                 latency: float,
                 token_usage: Optional[Dict[str, int]] = None,
                 conversation_id: Optional[str] = None,
                 error: Optional[str] = None) -> str:
        """
        Log an LLM API call to a JSON file.

        Returns the log file path.
        """
        timestamp = datetime.now()
        call_id = str(uuid.uuid4())

        log_entry = {
            "call_id": call_id,
            "timestamp": timestamp.isoformat(),
            "provider": provider,
            "call_type": call_type,
            "conversation_id": conversation_id,
            "latency_seconds": latency,
            "request": {
                "model": request_data.get("model"),
                "messages": request_data.get("messages", []),
                "tools": request_data.get("tools"),
                "max_tokens": request_data.get("max_tokens"),
                "temperature": request_data.get("temperature"),
                "system": request_data.get("system")  # For Anthropic
            },
            "response": {
                "content": response_data.get("content"),
                "tool_calls": response_data.get("tool_calls"),
                "finish_reason": response_data.get("finish_reason"),
                "raw_response": self._truncate_response(response_data.get("raw_response"))
            },
            "token_usage": token_usage or {},
            "error": error,
            "metadata": {
                "message_count": len(request_data.get("messages", [])),
                "tool_count": len(request_data.get("tools", [])) if request_data.get("tools") else 0,
                "has_system_prompt": bool(request_data.get("system")),
                "response_has_tool_calls": bool(response_data.get("tool_calls"))
            }
        }

        # Create filename with timestamp and call type
        filename = f"{timestamp.strftime('%Y%m%d_%H%M%S')}_{provider}_{call_type}_{call_id[:8]}.json"
        filepath = os.path.join(self.log_dir, filename)

        # Write to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(log_entry, f, indent=2, ensure_ascii=False)

        return filepath

    def _truncate_response(self, response: Any, max_length: int = 1000) -> Any:
        """Truncate response data to avoid huge log files."""
        if isinstance(response, str):
            return response[:max_length] + "..." if len(response) > max_length else response
        elif isinstance(response, dict):
            return {k: self._truncate_response(v, max_length) for k, v in response.items()}
        elif isinstance(response, list):
            return [self._truncate_response(item, max_length) for item in response]
        else:
            return response


class ToolCallResult:
    """Represents the result of a tool call, with additional metadata."""

    def __init__(self,
                 tool_name: str,
                 tool_input: Any,
                 tool_call_id: str,
                 success: bool = True,
                 content: Optional[str] = None,
                 error: Optional[str] = None,
                 execution_time: Optional[float] = None):
        self.tool_name = tool_name
        self.tool_input = tool_input
        self.tool_call_id = tool_call_id
        self.success = success
        self.content = content
        self.error = error
        self.execution_time = execution_time
        self.timestamp = None  # Can be set by the caller if needed

    def to_dict(self) -> Dict[str, Any]:
        """Convert to a dictionary for storage or serialization."""
        return {
            "tool_name": self.tool_name,
            "tool_input": self.tool_input,
            "tool_call_id": self.tool_call_id,
            "success": self.success,
            "content": self.content,
            "error": self.error,
            "execution_time": self.execution_time,
            "timestamp": self.timestamp
        }

    def to_tool_result_block(self, provider: str = "anthropic") -> Dict[str, Any]:
        """Convert to a tool result block for the specified LLM provider."""
        if provider == "anthropic":
            return self._to_anthropic_tool_result()
        elif provider == "openai":
            return self._to_openai_tool_result()
        elif provider == "gemini":
            return self._to_gemini_tool_result()
        else:
            raise ValueError(f"Unsupported provider: {provider}")

    def _to_anthropic_tool_result(self) -> Dict[str, Any]:
        """Convert to Anthropic tool result format."""
        result = {
            "type": "tool_result",
            "tool_use_id": self.tool_call_id,
        }

        if not self.success:
            result["is_error"] = True
            result["content"] = self.error or f"Error executing tool {self.tool_name}."
        else:
            result["content"] = self.content or ""

        return result

    def _to_openai_tool_result(self) -> Dict[str, Any]:
        """Convert to OpenAI tool result format."""
        # OpenAI expects tool results as regular messages with role "tool"
        content = self.content or ""

        # If content is a TextContent object, extract the text
        if hasattr(content, 'text'):
            content = content.text
        elif isinstance(content, list) and len(content) > 0 and hasattr(content[0], 'text'):
            content = content[0].text

        return {
            "role": "tool",
            "tool_call_id": self.tool_call_id,
            "content": str(content) if not self.success else str(content),
            "name": self.tool_name
        }

    def _to_gemini_tool_result(self) -> Dict[str, Any]:
        """Convert to Gemini tool result format."""
        # Gemini expects function responses in a specific format
        content = self.content or ""

        # If content is a TextContent object, extract the text
        if hasattr(content, 'text'):
            content = content.text
        elif isinstance(content, list) and len(content) > 0 and hasattr(content[0], 'text'):
            content = content[0].text

        return {
            "role": "function",
            "parts": [{
                "function_response": {
                    "name": self.tool_name,
                    "response": {
                        "success": self.success,
                        "content": str(content) if self.success else self.error or f"Error executing tool {self.tool_name}."
                    }
                }
            }]
        }


class MCPClientLib:
    """Core client library for interacting with MCP servers via streamable HTTP."""

    def __init__(self,
                 llm_provider: str = "anthropic",
                 anthropic_api_key: Optional[str] = None,
                 openai_api_key: Optional[str] = None,
                 gemini_api_key: Optional[str] = None,
                 debug_callback: Optional[Callable] = None,
                 conversation_id: Optional[str] = None):
        """Initialize the MCP client library.

        Args:
            llm_provider: LLM provider to use ("anthropic", "openai", or "gemini")
            anthropic_api_key: Optional API key for Anthropic. If not provided,
                               it will be loaded from environment variables.
            openai_api_key: Optional API key for OpenAI. If not provided,
                            it will be loaded from environment variables.
            gemini_api_key: Optional API key for Google Gemini. If not provided,
                            it will be loaded from environment variables.
            debug_callback: Optional callback function for debug messages.
                            Function signature: callback(level: str, message: str, data: Any)
        """
        # Initialize session and client objects
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.available_tools = []
        self.connected = False
        self.debug_callback = debug_callback
        self.llm_provider = llm_provider.lower()

        # Initialize LLM call logger
        self.llm_logger = LLMCallLogger()
        self.conversation_id = conversation_id

        # Initialize LLM clients
        self.anthropic = None
        self.openai = None
        self.gemini = None

        if self.llm_provider == "anthropic":
            self._init_anthropic(anthropic_api_key)
        elif self.llm_provider == "openai":
            self._init_openai(openai_api_key)
        elif self.llm_provider == "gemini":
            self._init_gemini(gemini_api_key)
        else:
            logger.error(f"Unsupported LLM provider: {self.llm_provider}")
            print(f"Unsupported LLM provider: {self.llm_provider}")

    def set_conversation_id(self, conversation_id: str):
        """Set the conversation ID for logging purposes."""
        self.conversation_id = conversation_id

    def _init_anthropic(self, api_key: Optional[str] = None):
        """Initialize Anthropic client."""
        try:
            import os
            # If API key is provided, use it
            if api_key:
                self.anthropic = Anthropic(api_key=api_key)
            # Otherwise, try to get it from environment variables
            elif "ANTHROPIC_API_KEY" in os.environ:
                self.anthropic = Anthropic(api_key=os.environ["ANTHROPIC_API_KEY"])
            else:
                # Try to create without explicit key (may use ~/.anthropic/config.json)
                self.anthropic = Anthropic()
                # Test if it works
                if not hasattr(self.anthropic, "api_key") or not self.anthropic.api_key:
                    logger.warning("No Anthropic API key found. LLM functionality will not work.")
                    print("No Anthropic API key found. LLM functionality will not work.")
        except Exception as e:
            logger.error(f"Error initializing Anthropic client: {e}")
            print(f"Error initializing Anthropic client: {e}")
            self.anthropic = None

    def _init_openai(self, api_key: Optional[str] = None):
        """Initialize OpenAI client."""
        if not OPENAI_AVAILABLE:
            logger.error("OpenAI library not available. Install with: pip install openai")
            print("OpenAI library not available. Install with: pip install openai")
            return

        try:
            import os
            # If API key is provided, use it
            if api_key:
                self.openai = OpenAI(api_key=api_key)
            # Otherwise, try to get it from environment variables
            elif "OPENAI_API_KEY" in os.environ:
                self.openai = OpenAI(api_key=os.environ["OPENAI_API_KEY"])
            else:
                # Try to create without explicit key
                self.openai = OpenAI()
        except Exception as e:
            logger.error(f"Error initializing OpenAI client: {e}")
            print(f"Error initializing OpenAI client: {e}")
            self.openai = None

    def _init_gemini(self, api_key: Optional[str] = None):
        """Initialize Google Gemini client."""
        if not GEMINI_AVAILABLE:
            logger.error("Google Generative AI library not available. Install with: pip install google-generativeai")
            print("Google Generative AI library not available. Install with: pip install google-generativeai")
            return

        try:
            import os
            # If API key is provided, use it
            if api_key:
                genai.configure(api_key=api_key)
            # Otherwise, try to get it from environment variables
            elif "GEMINI_API_KEY" in os.environ:
                genai.configure(api_key=os.environ["GEMINI_API_KEY"])
            elif "GOOGLE_API_KEY" in os.environ:
                genai.configure(api_key=os.environ["GOOGLE_API_KEY"])
            else:
                logger.error("No Gemini API key found. Set GEMINI_API_KEY or GOOGLE_API_KEY environment variable.")
                print("No Gemini API key found. Set GEMINI_API_KEY or GOOGLE_API_KEY environment variable.")
                self.gemini = None
                return

            # Create the model instance
            self.gemini = genai.GenerativeModel('gemini-2.0-flash-exp')
            print("✅ Gemini client initialized successfully!")
        except Exception as e:
            logger.error(f"Error initializing Gemini client: {e}")
            print(f"Error initializing Gemini client: {e}")
            self.gemini = None

    def _call_llm_api(self, messages: List[Dict], system_prompt: Optional[str] = None,
                      model: str = None, max_tokens: int = 1024, tools: Optional[List] = None,
                      call_type: str = "unknown", conversation_id: Optional[str] = None):
        """Call the appropriate LLM API based on the provider with logging."""
        start_time = time.time()

        # Prepare request data for logging
        request_data = {
            "model": model or DEFAULT_MODEL,
            "messages": messages,
            "system": system_prompt,
            "max_tokens": max_tokens,
            "tools": tools
        }

        try:
            # Make the actual API call
            if self.llm_provider == "anthropic":
                response = self._call_anthropic_api(messages, system_prompt, model, max_tokens, tools)
            elif self.llm_provider == "openai":
                response = self._call_openai_api(messages, system_prompt, model, max_tokens, tools)
            elif self.llm_provider == "gemini":
                response = self._call_gemini_api(messages, system_prompt, model, max_tokens, tools)
            else:
                raise ValueError(f"Unsupported LLM provider: {self.llm_provider}")

            # Calculate latency
            latency = time.time() - start_time

            # Extract token usage and response data for logging
            token_usage = self._extract_token_usage(response)
            response_data = self._extract_response_data(response)

            # Log the successful call
            log_path = self.llm_logger.log_call(
                provider=self.llm_provider,
                call_type=call_type,
                request_data=request_data,
                response_data=response_data,
                latency=latency,
                token_usage=token_usage,
                conversation_id=conversation_id
            )

            print(f"    📝 Logged {self.llm_provider.upper()} call to: {log_path}")

            return response

        except Exception as e:
            # Calculate latency even for failed calls
            latency = time.time() - start_time

            # Log the failed call
            log_path = self.llm_logger.log_call(
                provider=self.llm_provider,
                call_type=call_type,
                request_data=request_data,
                response_data={},
                latency=latency,
                conversation_id=conversation_id,
                error=str(e)
            )

            print(f"    📝 Logged failed {self.llm_provider.upper()} call to: {log_path}")

            # Re-raise the exception
            raise

    def _extract_token_usage(self, response) -> Dict[str, int]:
        """Extract token usage information from API response."""
        try:
            if self.llm_provider == "anthropic":
                if hasattr(response, 'usage'):
                    return {
                        "input_tokens": getattr(response.usage, 'input_tokens', 0),
                        "output_tokens": getattr(response.usage, 'output_tokens', 0),
                        "total_tokens": getattr(response.usage, 'input_tokens', 0) + getattr(response.usage, 'output_tokens', 0)
                    }
            elif self.llm_provider == "openai":
                if hasattr(response, 'usage'):
                    return {
                        "prompt_tokens": getattr(response.usage, 'prompt_tokens', 0),
                        "completion_tokens": getattr(response.usage, 'completion_tokens', 0),
                        "total_tokens": getattr(response.usage, 'total_tokens', 0)
                    }
            elif self.llm_provider == "gemini":
                if hasattr(response, 'usage_metadata'):
                    return {
                        "prompt_token_count": getattr(response.usage_metadata, 'prompt_token_count', 0),
                        "candidates_token_count": getattr(response.usage_metadata, 'candidates_token_count', 0),
                        "total_token_count": getattr(response.usage_metadata, 'total_token_count', 0)
                    }
        except Exception:
            pass
        return {}

    def _extract_response_data(self, response) -> Dict[str, Any]:
        """Extract response data for logging."""
        try:
            response_data = {
                "raw_response": str(response)  # Will be truncated by logger
            }

            if self.llm_provider == "anthropic":
                if hasattr(response, 'content'):
                    response_data["content"] = [
                        {"type": block.type, "text": getattr(block, 'text', '')}
                        for block in response.content
                    ]
                if hasattr(response, 'stop_reason'):
                    response_data["finish_reason"] = response.stop_reason

            elif self.llm_provider == "openai":
                if hasattr(response, 'choices') and response.choices:
                    choice = response.choices[0]
                    if hasattr(choice, 'message'):
                        message = choice.message
                        response_data["content"] = getattr(message, 'content', '')
                        if hasattr(message, 'tool_calls') and message.tool_calls:
                            response_data["tool_calls"] = [
                                {
                                    "id": tc.id,
                                    "type": tc.type,
                                    "function": {
                                        "name": tc.function.name,
                                        "arguments": tc.function.arguments
                                    }
                                }
                                for tc in message.tool_calls
                            ]
                    if hasattr(choice, 'finish_reason'):
                        response_data["finish_reason"] = choice.finish_reason

            elif self.llm_provider == "gemini":
                if hasattr(response, 'candidates') and response.candidates:
                    candidate = response.candidates[0]
                    if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                        content_parts = []
                        for part in candidate.content.parts:
                            if hasattr(part, 'text'):
                                content_parts.append(part.text)
                            elif hasattr(part, 'function_call'):
                                # Handle function calls for tool use
                                if "tool_calls" not in response_data:
                                    response_data["tool_calls"] = []
                                response_data["tool_calls"].append({
                                    "name": part.function_call.name,
                                    "args": dict(part.function_call.args)
                                })
                        response_data["content"] = " ".join(content_parts)
                    if hasattr(candidate, 'finish_reason'):
                        response_data["finish_reason"] = str(candidate.finish_reason)

            return response_data
        except Exception:
            return {"raw_response": str(response)}

    def _call_anthropic_api(self, messages: List[Dict], system_prompt: Optional[str] = None,
                           model: str = None, max_tokens: int = 1024, tools: Optional[List] = None):
        """Call Anthropic API."""
        api_params = {
            "model": model or DEFAULT_MODEL,
            "max_tokens": max_tokens,
            "messages": messages,
        }

        if tools:
            api_params["tools"] = tools
        if system_prompt:
            api_params["system"] = system_prompt

        return self.anthropic.messages.create(**api_params)

    def _call_openai_api(self, messages: List[Dict], system_prompt: Optional[str] = None,
                        model: str = None, max_tokens: int = 1024, tools: Optional[List] = None):
        """Call OpenAI API."""
        # OpenAI handles system messages differently - they go in the messages array
        openai_messages = []

        # Add system message if provided
        if system_prompt:
            openai_messages.append({"role": "system", "content": system_prompt})

        # Add conversation messages
        openai_messages.extend(messages)

        api_params = {
            "model": model or "gpt-4o-mini",
            "max_tokens": max_tokens,
            "messages": openai_messages,
        }

        if tools:
            # Convert MCP tools to OpenAI format
            openai_tools = []
            for tool in tools:
                openai_tool = {
                    "type": "function",
                    "function": {
                        "name": tool["name"],
                        "description": tool["description"],
                        "parameters": tool.get("inputSchema", {})
                    }
                }
                openai_tools.append(openai_tool)
            api_params["tools"] = openai_tools

        return self.openai.chat.completions.create(**api_params)

    def _call_gemini_api(self, messages: List[Dict], system_prompt: Optional[str] = None,
                         model: str = None, max_tokens: int = 1024, tools: Optional[List] = None):
        """Call Google Gemini API."""
        if not self.gemini:
            raise ValueError("Gemini client not initialized")

        # Convert messages to Gemini format
        gemini_messages = []

        # Add system prompt if provided
        if system_prompt:
            gemini_messages.append({
                "role": "user",
                "parts": [{"text": f"System: {system_prompt}"}]
            })

        # Convert messages
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")

            # Handle function messages specially (they're already in Gemini format)
            if role == "function":
                # Function messages are already in the correct Gemini format
                gemini_messages.append(msg)
                continue

            # Map roles to Gemini format
            if role == "assistant":
                gemini_role = "model"
            else:
                gemini_role = "user"

            # Handle different content types
            if isinstance(content, str):
                gemini_messages.append({
                    "role": gemini_role,
                    "parts": [{"text": content}]
                })
            elif isinstance(content, list):
                parts = []
                for item in content:
                    if isinstance(item, dict):
                        if item.get("type") == "text":
                            parts.append({"text": item.get("text", "")})
                        elif item.get("type") == "tool_use":
                            # Convert tool_use to Gemini function_call format
                            parts.append({
                                "function_call": {
                                    "name": item.get("name", ""),
                                    "args": item.get("input", {})
                                }
                            })
                        elif item.get("type") == "tool_result":
                            # Handle tool results
                            tool_result = item.get("content", "")
                            if isinstance(tool_result, list):
                                tool_result = " ".join([str(r.get("text", r)) for r in tool_result])
                            parts.append({"text": f"Tool result: {tool_result}"})
                    else:
                        parts.append({"text": str(item)})

                if parts:
                    gemini_messages.append({
                        "role": gemini_role,
                        "parts": parts
                    })

        # Convert tools to Gemini function declarations
        function_declarations = []
        if tools:
            for tool in tools:
                # Handle MCP tool format (direct format)
                if isinstance(tool, dict) and "name" in tool:
                    # Clean the schema by removing unsupported fields like "title"
                    clean_schema = self._clean_schema_for_gemini(tool.get("input_schema", {}))
                    function_declarations.append({
                        "name": tool["name"],
                        "description": tool.get("description", ""),
                        "parameters": clean_schema
                    })
                # Handle OpenAI tool format (with "function" wrapper)
                elif isinstance(tool, dict) and "function" in tool:
                    func_def = tool["function"]
                    clean_schema = self._clean_schema_for_gemini(func_def.get("parameters", {}))
                    function_declarations.append({
                        "name": func_def["name"],
                        "description": func_def.get("description", ""),
                        "parameters": clean_schema
                    })

        # Prepare generation config
        generation_config = {
            "max_output_tokens": max_tokens,
            "temperature": 0.1,
        }

        # Make the API call
        if function_declarations:
            # Use function calling
            response = self.gemini.generate_content(
                gemini_messages,
                generation_config=generation_config,
                tools=[{"function_declarations": function_declarations}]
            )
        else:
            # Regular generation
            response = self.gemini.generate_content(
                gemini_messages,
                generation_config=generation_config
            )

        return response

    def _clean_schema_for_gemini(self, schema):
        """Clean JSON schema for Gemini by removing unsupported fields."""
        if not isinstance(schema, dict):
            return schema

        # Fields that Gemini doesn't support in function declarations
        unsupported_fields = {"title", "default", "anyOf", "allOf", "oneOf", "additionalProperties"}

        cleaned = {}
        for key, value in schema.items():
            if key in unsupported_fields:
                # Skip unsupported fields
                continue
            elif isinstance(value, dict):
                # Recursively clean nested objects
                cleaned[key] = self._clean_schema_for_gemini(value)
            elif isinstance(value, list):
                # Clean items in arrays
                cleaned[key] = [
                    self._clean_schema_for_gemini(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                # Keep other fields as-is
                cleaned[key] = value

        return cleaned

    def _parse_response(self, response):
        """Parse response from Anthropic, OpenAI, or Gemini."""
        if self.llm_provider == "anthropic":
            return self._parse_anthropic_response(response)
        elif self.llm_provider == "openai":
            return self._parse_openai_response(response)
        elif self.llm_provider == "gemini":
            return self._parse_gemini_response(response)
        else:
            raise ValueError(f"Unsupported LLM provider: {self.llm_provider}")

    def _parse_anthropic_response(self, response):
        """Parse Anthropic response format."""
        return {
            "content": response.content,
            "text_blocks": [block for block in response.content if block.type == 'text'],
            "tool_use_blocks": [block for block in response.content if block.type == 'tool_use']
        }

    def _parse_openai_response(self, response):
        """Parse OpenAI response format and convert to Anthropic-like structure."""
        message = response.choices[0].message
        content_blocks = []

        # Handle text content
        if message.content:
            # Create a text block similar to Anthropic format
            class TextBlock:
                def __init__(self, text):
                    self.type = 'text'
                    self.text = text
            content_blocks.append(TextBlock(message.content))

        # Handle tool calls
        tool_use_blocks = []
        if hasattr(message, 'tool_calls') and message.tool_calls:
            for tool_call in message.tool_calls:
                # Create a tool use block similar to Anthropic format
                class ToolUseBlock:
                    def __init__(self, tool_call):
                        self.type = 'tool_use'
                        self.id = tool_call.id
                        self.name = tool_call.function.name
                        import json
                        self.input = json.loads(tool_call.function.arguments)

                tool_block = ToolUseBlock(tool_call)
                content_blocks.append(tool_block)
                tool_use_blocks.append(tool_block)

        return {
            "content": content_blocks,
            "text_blocks": [block for block in content_blocks if block.type == 'text'],
            "tool_use_blocks": tool_use_blocks
        }

    def _parse_gemini_response(self, response):
        """Parse Gemini response format."""
        content_blocks = []
        tool_use_blocks = []

        if hasattr(response, 'candidates') and response.candidates:
            candidate = response.candidates[0]
            if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts') and candidate.content.parts:
                for part in candidate.content.parts:
                    if hasattr(part, 'text'):
                        # Text content
                        class TextBlock:
                            def __init__(self, text):
                                self.type = 'text'
                                self.text = text
                        content_blocks.append(TextBlock(part.text))
                    if hasattr(part, 'function_call') and part.function_call and hasattr(part.function_call, 'name'):
                        # Function call (tool use) - only process if it has valid args
                        if hasattr(part.function_call, 'args') and part.function_call.args is not None:
                            class ToolUseBlock:
                                def __init__(self, function_call):
                                    self.type = 'tool_use'
                                    self.id = f"gemini_tool_{hash(function_call.name)}"
                                    self.name = function_call.name
                                    self.input = dict(function_call.args)

                            tool_block = ToolUseBlock(part.function_call)
                            content_blocks.append(tool_block)
                            tool_use_blocks.append(tool_block)
        return {
            "content": content_blocks,
            "text_blocks": [block for block in content_blocks if block.type == 'text'],
            "tool_use_blocks": tool_use_blocks
        }

    async def _debug(self, level: str, message: str, data: Any = None):
        """Send debug information to the callback if provided."""
        if self.debug_callback:
            try:
                # Call the debug callback and await it if it's a coroutine
                result = self.debug_callback(level, message, data)
                if asyncio.iscoroutine(result):
                    await result
            except Exception as e:
                # Don't let debug callback errors affect the main flow
                logger.error(f"Error in debug callback: {e}")

        # Also log using the standard logging module
        if level == "info":
            logger.info(message)
        elif level == "warning":
            logger.warning(message)
        elif level == "error":
            logger.error(message)
        elif level == "debug":
            logger.debug(message)

    async def connect_to_server(self, server_url: str = DEFAULT_SERVER_URL) -> bool:
        """Connect to an MCP server via streamable HTTP transport.

        Args:
            server_url: The HTTP URL of the server's streamable HTTP endpoint.

        Returns:
            bool: True if connection was successful, False otherwise.
        """
        await self._debug("info", f"Connecting to MCP server via streamable HTTP at: {server_url}")

        try:
            # Establish streamable HTTP transport connection using the provided URL
            await self._debug("info", f"🔍 HTTP TRANSPORT: Connecting to {server_url}")
            http_transport = await self.exit_stack.enter_async_context(streamablehttp_client(server_url))
            read_stream, write_stream, _ = http_transport  # Ignore the session ID callback
            await self._debug("info", f"🔍 HTTP TRANSPORT: Connection established, streams: {type(read_stream)}, {type(write_stream)}")
            await self._debug("debug", "Streamable HTTP transport connection established successfully.")

            # Create and initialize the MCP ClientSession using the HTTP streams
            self.session = await self.exit_stack.enter_async_context(ClientSession(read_stream, write_stream))
            await self._debug("info", "Initializing MCP session (performing handshake)...")
            await self.session.initialize()
            await self._debug("info", "MCP session initialized successfully.")

            # List available tools from the connected server
            await self._debug("info", "🔍 DEBUG: About to call list_tools()")
            try:
                response = await self.session.list_tools()
                await self._debug("info", f"🔍 DEBUG: list_tools() succeeded, response type: {type(response)}")
                tools = response.tools
                await self._debug("info", f"🔍 DEBUG: Got tools from response: {type(tools)}")
            except Exception as e:
                await self._debug("error", f"🔍 DEBUG: list_tools() failed: {e}")
                await self._debug("error", f"🔍 DEBUG: Exception type: {type(e)}")
                import traceback
                await self._debug("error", f"🔍 DEBUG: Traceback: {traceback.format_exc()}")
                raise

            # DEBUG: Log what we actually received
            await self._debug("info", f"🔍 DEBUG: response type: {type(response)}")
            await self._debug("info", f"🔍 DEBUG: tools type: {type(tools)}")
            await self._debug("info", f"🔍 DEBUG: tools length: {len(tools) if hasattr(tools, '__len__') else 'no length'}")
            
            if tools and len(tools) > 0:
                await self._debug("info", f"🔍 DEBUG: first tool type: {type(tools[0])}")
                await self._debug("info", f"🔍 DEBUG: first tool repr: {repr(tools[0])}")
                if hasattr(tools[0], '__dict__'):
                    await self._debug("info", f"🔍 DEBUG: first tool dict: {tools[0].__dict__}")
                await self._debug("info", f"🔍 DEBUG: first tool dir: {dir(tools[0])}")

            # Handle both dict and object formats for tools
            tool_names = []
            for tool in tools:
                if hasattr(tool, 'name'):
                    tool_names.append(tool.name)
                elif isinstance(tool, dict) and 'name' in tool:
                    tool_names.append(tool['name'])
                else:
                    tool_names.append(str(tool))

            await self._debug("info", f"Connected to server with tools: {', '.join(tool_names)}", tools)

            # Prepare tools in the format expected by Anthropic API
            self.available_tools = []
            for tool in tools:
                if hasattr(tool, 'name'):
                    # Object format - debug the attributes
                    await self._debug("info", f"Tool object attributes: {dir(tool)}")
                    await self._debug("info", f"Tool type: {type(tool)}")
                    
                    # Handle different possible attribute names
                    description = getattr(tool, 'description', None) or getattr(tool, 'desc', '') or f"Tool: {tool.name}"
                    input_schema = getattr(tool, 'inputSchema', None) or getattr(tool, 'input_schema', {})
                    
                    self.available_tools.append({
                        "name": tool.name,
                        "description": description,
                        "input_schema": input_schema
                    })
                elif isinstance(tool, dict):
                    # Dict format
                    self.available_tools.append({
                        "name": tool.get('name', 'unknown'),
                        "description": tool.get('description', ''),
                        "input_schema": tool.get('inputSchema', {})
                    })

            self.connected = True
            return True

        except ConnectionRefusedError as e:
            await self._debug("error", f"Connection Error: Could not connect to the server at {server_url}.", e)
            await self._debug("error", "Please ensure the MCP server process is running, accessible, and listening on the correct address and port with streamable HTTP enabled.")
            self.connected = False
            return False

        except Exception as e:
            await self._debug("error", f"An unexpected error occurred during connection or initialization: {e}", e)
            self.connected = False
            return False

    async def call_tool(self,
                        tool_name: str,
                        tool_input: Any,
                        tool_call_id: str = "manual_call",
                        timeout: Optional[float] = None) -> ToolCallResult:
        """Call a tool via the MCP session with optional timeout.

        Args:
            tool_name: Name of the tool to call
            tool_input: Input for the tool
            tool_call_id: ID for the tool call (for tracking)
            timeout: Optional timeout in seconds

        Returns:
            ToolCallResult: Object containing the result and metadata
        """
        if not self.session:
            return ToolCallResult(
                tool_name=tool_name,
                tool_input=tool_input,
                tool_call_id=tool_call_id,
                success=False,
                error="Not connected to MCP server"
            )

        # Ensure tool_input is properly formatted as a dictionary
        formatted_input = tool_input

        # Handle echostring tool specifically
        if tool_name == "echostring":
            # Case 1: If input is a string, convert to {"phrase": string}
            if isinstance(tool_input, str):
                formatted_input = {"phrase": tool_input}
                await self._debug("info", f"Converted string input to dictionary: {formatted_input}")
            # Case 2: If input is a dict without "phrase" key but with other keys
            elif isinstance(tool_input, dict) and "phrase" not in tool_input and len(tool_input) > 0:
                # Use the first value as the phrase
                first_key = next(iter(tool_input))
                formatted_input = {"phrase": tool_input[first_key]}
                await self._debug("info", f"Extracted phrase from dictionary: {formatted_input}")

        await self._debug("info", f"Calling tool: '{tool_name}' with input: {formatted_input}")

        import time
        start_time = time.time()

        try:
            # Call the tool with timeout if specified
            if timeout:
                task = asyncio.create_task(self.session.call_tool(tool_name, formatted_input))
                try:
                    mcp_result = await asyncio.wait_for(task, timeout=timeout)
                except asyncio.TimeoutError:
                    task.cancel()
                    execution_time = time.time() - start_time
                    return ToolCallResult(
                        tool_name=tool_name,
                        tool_input=tool_input,  # Keep original input for reference
                        tool_call_id=tool_call_id,
                        success=False,
                        error=f"Tool call timed out after {timeout} seconds",
                        execution_time=execution_time
                    )
            else:
                mcp_result = await self.session.call_tool(tool_name, formatted_input)

            # 🔍 DEBUG: Raw MCP result inspection
            await self._debug("info", f"🔍 RAW MCP RESULT: type={type(mcp_result)}")
            await self._debug("info", f"🔍 RAW MCP RESULT: hasattr content={hasattr(mcp_result, 'content')}")
            if hasattr(mcp_result, 'content'):
                await self._debug("info", f"🔍 RAW MCP RESULT: content type={type(mcp_result.content)}")
                await self._debug("info", f"🔍 RAW MCP RESULT: content repr={repr(mcp_result.content)[:200]}")

            execution_time = time.time() - start_time

            # Process the result
            if mcp_result.isError:
                await self._debug("warning", f"Tool '{tool_name}' reported an error.", mcp_result)
                return ToolCallResult(
                    tool_name=tool_name,
                    tool_input=tool_input,
                    tool_call_id=tool_call_id,
                    success=False,
                    error=mcp_result.content if hasattr(mcp_result, 'content') else "Unknown error",
                    execution_time=execution_time
                )
            else:
                await self._debug("info", f"Tool '{tool_name}' executed successfully.", mcp_result)
                return ToolCallResult(
                    tool_name=tool_name,
                    tool_input=tool_input,
                    tool_call_id=tool_call_id,
                    success=True,
                    content=mcp_result.content if hasattr(mcp_result, 'content') else str(mcp_result),
                    execution_time=execution_time
                )

        except Exception as e:
            execution_time = time.time() - start_time
            await self._debug("error", f"Error calling tool '{tool_name}' via MCP: {e}", e)
            return ToolCallResult(
                tool_name=tool_name,
                tool_input=tool_input,
                tool_call_id=tool_call_id,
                success=False,
                error=f"Client-side error calling tool {tool_name}: {e}",
                execution_time=execution_time
            )

    async def process_query(self,
                           query: str,
                           model: str = DEFAULT_MODEL,
                           max_tokens: int = 1024,
                           tool_timeout: Optional[float] = None,
                           conversation_history: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Process a query using Claude and available tools via the MCP session.

        Args:
            query: The user query to process
            model: Claude model to use
            max_tokens: Maximum tokens for Claude response
            tool_timeout: Optional timeout for tool calls in seconds
            conversation_history: Optional conversation history to include

        Returns:
            Dict containing:
                - final_text: The final response text
                - messages: Updated conversation history
                - tool_results: List of tool call results
                - error: Error message if any
        """
        # Initialize detailed latency tracker
        latency_tracker = LatencyTracker()
        latency_tracker.start_total()

        if not self.session:
            return {
                "final_text": "Error: Not connected to MCP server.",
                "messages": [],
                "tool_results": [],
                "error": "Not connected to MCP server"
            }

        # Check if the appropriate LLM client is available
        if self.llm_provider == "anthropic" and not self.anthropic:
            return {
                "final_text": "Error: Anthropic API client is not available. Please check your API key.",
                "messages": conversation_history.copy() if conversation_history else [],
                "tool_results": [],
                "error": "Anthropic API client is not available. Please check your API key."
            }
        elif self.llm_provider == "openai" and not self.openai:
            return {
                "final_text": "Error: OpenAI API client is not available. Please check your API key.",
                "messages": conversation_history.copy() if conversation_history else [],
                "tool_results": [],
                "error": "OpenAI API client is not available. Please check your API key."
            }

        # Step 1: Message Preparation
        latency_tracker.start_step("Message Preparation")

        # Initialize or use provided conversation history
        all_messages = conversation_history.copy() if conversation_history else []

        # Extract system messages and filter them out from the messages array
        system_messages = []
        messages = []
        for msg in all_messages:
            if msg.get("role") == "system":
                system_messages.append(msg["content"])
            else:
                messages.append(msg)

        # Combine system messages into a single system prompt
        system_prompt = "\n".join(system_messages) if system_messages else None

        # Add the user query if not already in history
        if not messages or messages[-1]["role"] != "user" or messages[-1]["content"] != query:
            messages.append({"role": "user", "content": query})

        final_response_parts = []  # Store parts of the final response text
        tool_results = []  # Store tool call results

        prep_time = latency_tracker.end_step("Message Preparation")
        print(f"    ⏱️  Message preparation: {prep_time:.3f}s")

        await self._debug("info", "Starting query processing", {"query": query, "messages": messages})

        try:
            # Step 2: Initial LLM API Call
            latency_tracker.start_step("Initial LLM API Call")
            await self._debug("debug", f"Calling {self.llm_provider.upper()} API with tools", self.available_tools)

            response = self._call_llm_api(
                messages=messages,
                system_prompt=system_prompt,
                model=model,
                max_tokens=max_tokens,
                tools=self.available_tools,
                call_type="initial",
                conversation_id=self.conversation_id
            )

            initial_api_time = latency_tracker.end_step("Initial LLM API Call")
            print(f"    ⏱️  Initial {self.llm_provider.upper()} API call: {initial_api_time:.3f}s")

            await self._debug("debug", f"Received initial {self.llm_provider.upper()} response", response)

            # Parse response using provider-specific parser
            parsed_response = self._parse_response(response)

            # Append initial assistant message(s) to conversation history (format depends on provider)
            if self.llm_provider == "openai":
                # OpenAI format: assistant message with content and tool_calls
                assistant_message = {"role": "assistant"}

                # Add text content if any
                text_content = ""
                text_blocks = parsed_response.get("text_blocks", []) or []
                for content_block in text_blocks:
                    text_content += content_block.text
                    final_response_parts.append(content_block.text)

                if text_content:
                    assistant_message["content"] = text_content

                # Add tool calls if any
                if parsed_response["tool_use_blocks"]:
                    tool_calls = []
                    for tool_block in parsed_response["tool_use_blocks"]:
                        tool_call = {
                            "id": tool_block.id,
                            "type": "function",
                            "function": {
                                "name": tool_block.name,
                                "arguments": json.dumps(tool_block.input)
                            }
                        }
                        tool_calls.append(tool_call)
                    assistant_message["tool_calls"] = tool_calls

                messages.append(assistant_message)
            else:
                # Anthropic format: assistant message with content blocks
                assistant_content_blocks = []
                content_blocks = parsed_response.get("content", []) or []
                for content_block in content_blocks:
                    # Add text blocks immediately to final response
                    if content_block.type == 'text':
                        final_response_parts.append(content_block.text)
                        assistant_content_blocks.append({"type": "text", "text": content_block.text})
                    elif content_block.type == 'tool_use':
                        # Don't add tool use block to final text yet
                        assistant_content_blocks.append({
                            "type": "tool_use",
                            "id": content_block.id,
                            "name": content_block.name,
                            "input": content_block.input,
                        })

                if assistant_content_blocks:
                    messages.append({"role": "assistant", "content": assistant_content_blocks})

            # Process tool calls if any were requested
            tool_use_blocks = parsed_response.get("tool_use_blocks", []) or []

            if tool_use_blocks:
                # Step 3: Tool Execution
                latency_tracker.start_step("Tool Execution")
                await self._debug("info", f"Claude requested {len(tool_use_blocks)} tool call(s).")
                tool_results_content = []  # Content for the next user message to Claude

                for tool_use in tool_use_blocks:
                    tool_name = tool_use.name
                    tool_input = tool_use.input
                    tool_call_id = tool_use.id  # Get the ID for the result

                    await self._debug("info", f"Processing tool call: '{tool_name}'", tool_use)
                    # Add placeholder text to final response
                    final_response_parts.append(f"\n[Calling tool '{tool_name}'...]")

                    # Execute the tool call via MCP session
                    tool_result = await self.call_tool(
                        tool_name=tool_name,
                        tool_input=tool_input,
                        tool_call_id=tool_call_id,
                        timeout=tool_timeout
                    )

                    # Store the tool result for return
                    tool_results.append(tool_result)

                    # Convert to tool result block for the appropriate provider
                    tool_result_block = tool_result.to_tool_result_block(self.llm_provider)
                    tool_results_content.append(tool_result_block)

                    # Update final response based on tool result
                    if not tool_result.success:
                        final_response_parts.append(f"[Failed to call tool '{tool_name}'.]")
                    else:
                        final_response_parts.append(f"\n")

                tool_exec_time = latency_tracker.end_step("Tool Execution")
                print(f"    ⏱️  Tool execution: {tool_exec_time:.3f}s")

                await self._debug("debug", "Tool results content", tool_results_content)

                # Step 4: Follow-up LLM API Call
                latency_tracker.start_step("Follow-up LLM API Call")

                # Send tool results back to LLM (format depends on provider)
                if self.llm_provider == "openai":
                    # OpenAI expects tool results as individual messages with role "tool"
                    for tool_result_msg in tool_results_content:
                        messages.append(tool_result_msg)
                elif self.llm_provider == "gemini":
                    # Gemini expects tool results as individual messages with role "function"
                    for tool_result_msg in tool_results_content:
                        messages.append(tool_result_msg)
                else:
                    # Anthropic expects tool results in a single user message
                    messages.append({
                        "role": "user",
                        "content": tool_results_content
                    })

                # Get LLM's response summarizing tool results
                await self._debug("debug", f"Calling {self.llm_provider.upper()} API with tool results")

                follow_up_response = self._call_llm_api(
                    messages=messages,
                    system_prompt=system_prompt,
                    model=model,
                    max_tokens=max_tokens,
                    tools=None,  # No tools needed for this follow-up
                    call_type="followup",
                    conversation_id=self.conversation_id
                )

                followup_api_time = latency_tracker.end_step("Follow-up LLM API Call")
                print(f"    ⏱️  Follow-up {self.llm_provider.upper()} API call: {followup_api_time:.3f}s")

                await self._debug("debug", f"Received follow-up {self.llm_provider.upper()} response", follow_up_response)

                # Parse follow-up response and add text content
                parsed_followup = self._parse_response(follow_up_response)
                text_blocks = parsed_followup.get("text_blocks", []) or []
                for content_block in text_blocks:
                    final_response_parts.append(content_block.text)

            # Step 5: Response Assembly
            latency_tracker.start_step("Response Assembly")
            # Join all collected response parts
            final_text = "\n".join(final_response_parts).strip()
            assembly_time = latency_tracker.end_step("Response Assembly")
            print(f"    ⏱️  Response assembly: {assembly_time:.3f}s")

            # Print detailed MCP latency summary
            latency_tracker.print_summary("    ")

            return {
                "final_text": final_text,
                "messages": messages,
                "tool_results": tool_results,
                "error": None
            }

        except Exception as e:
            await self._debug("error", f"Error during query processing: {e}", e)
            # Print latency summary even on error
            latency_tracker.print_summary("    ")
            return {
                "final_text": f"An error occurred: {e}",
                "messages": messages,
                "tool_results": tool_results,
                "error": str(e)
            }

    async def cleanup(self):
        """Clean up resources with improved error handling."""
        await self._debug("info", "Cleaning up resources...")

        # Mark as disconnected first to prevent new operations
        self.connected = False

        try:
            # Try graceful cleanup with timeout
            await asyncio.wait_for(self.exit_stack.aclose(), timeout=2.0)
            await self._debug("info", "Cleanup complete.")
        except asyncio.TimeoutError:
            await self._debug("info", "Cleanup timeout - resources may not be fully cleaned")
        except Exception as e:
            await self._debug("info", f"Cleanup warning: {e} - continuing anyway")

        # Clear session reference if it exists
        if hasattr(self, 'session'):
            self.session = None


# Example usage in a command-line client
if __name__ == "__main__":
    import sys

    def debug_callback(level, message, data=None):
        """Simple debug callback that prints to console."""
        print(f"[{level.upper()}] {message}")
        if data and level == "debug":
            print(f"  Data: {data}")
        # Return None to indicate this is not a coroutine
        return None

    async def main():
        # Determine server URL: use command-line arg or default
        server_url = sys.argv[1] if len(sys.argv) > 1 else DEFAULT_SERVER_URL

        # Create client with debug callback
        client = MCPClientLib(debug_callback=debug_callback)

        try:
            # Connect to server
            success = await client.connect_to_server(server_url)
            if not success:
                print("Failed to connect to server. Exiting.")
                return

            # Simple chat loop
            print("\nMCP Client Library Test")
            print("Type your queries or 'quit' to exit.")

            while True:
                query = input("\nQuery: ").strip()

                if not query:
                    continue
                if query.lower() == 'quit':
                    print("Exiting chat loop.")
                    break

                # Process the query
                result = await client.process_query(
                    query=query,
                    model=DEFAULT_MODEL,
                    max_tokens=1024,
                    tool_timeout=3600  # long timeout for long-running tools
                )

                # Print the result
                if result["error"]:
                    print(f"Error: {result['error']}")
                else:
                    print(f"\nResponse: {result['final_text']}")

        finally:
            # Ensure cleanup runs
            await client.cleanup()
            print("Cleanup complete.")

    # Run the main function
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())
